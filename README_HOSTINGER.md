# 🎬 Descargador M3U para Hostinger

Sistema completo para descargar series desde archivos M3U y subirlas automáticamente a servidores remotos via SFTP.

## 🌐 Arquitectura del Sistema

```
Internet → Hostinger (descarga) → <PERSON><PERSON><PERSON> (almacenamiento)
```

- **Hostinger**: Eje<PERSON>a el script PHP, descarga archivos con wget
- **Ser<PERSON><PERSON>**: Almacena las series organizadas por carpetas

## 📁 Archivos Principales

### Archivos Core:
- `index.php` - Interfaz web principal
- `m3u_series_downloader.php` - Procesador de archivos M3U
- `hostinger_directory_browser.php` - Explorador de directorios remotos
- `download_handler.php` - Manejador de descargas (Linux)
- `download_handler_windows.php` - Manejador de descargas (Windows local)

### Archivos de Configuración:
- `config.php` - Configuración general
- `check_hostinger_setup.php` - Verificación del entorno

### Archivos de Prueba:
- `test_extensions.php` - Verificar extensiones PHP
- `test_wget.php` - Probar wget y comandos
- `test_hostinger_setup.php` - Verificar entorno Hostinger

## 🚀 Instalación en Hostinger

### 1. Subir Archivos
```bash
# Subir todos los archivos PHP al directorio web de Hostinger
# Ejemplo: public_html/m3u-downloader/
```

### 2. Configurar Permisos
```bash
chmod 755 *.php
mkdir downloads uploads temp
chmod 777 downloads uploads temp
```

### 3. Verificar Entorno
Accede a: `https://tu-dominio.com/m3u-downloader/check_hostinger_setup.php`

## 🔧 Configuración

### 1. Editar config.php
```php
<?php
return [
    'download' => [
        'base_directory' => './downloads/',
        'timeout' => 300,
        'max_retries' => 3,
        'use_wget' => true,
    ],
    'sftp' => [
        'timeout' => 30,
        'max_connections' => 5,
    ]
];
?>
```

### 2. Configurar Servidor Remoto
En la interfaz web, configura:
- **Host**: IP o dominio de tu servidor remoto
- **Puerto**: 22 (SSH estándar)
- **Usuario**: usuario con acceso SFTP
- **Contraseña**: contraseña del usuario

## 📋 Uso del Sistema

### 1. Acceder a la Interfaz
```
https://tu-dominio.com/m3u-downloader/
```

### 2. Configurar Conexión SFTP
1. Introduce credenciales del servidor remoto
2. Haz clic en "🔍 Probar Conexión"
3. Haz clic en "🔗 Establecer Conexión"

### 3. Seleccionar Ruta de Descarga
1. Haz clic en "📁 Explorar Servidor"
2. Navega por las carpetas del servidor remoto
3. Selecciona una ruta como `/home/<USER>/series`

### 4. Subir M3U y Descargar
1. Sube tu archivo M3U con las series
2. Selecciona las series a descargar
3. Haz clic en "🚀 Iniciar Descarga"

## 🗂️ Estructura de Archivos Resultante

```
/home/<USER>/series/
├── Breaking Bad/
│   ├── Temporada 1/
│   │   ├── S01E01 - Pilot.mp4
│   │   ├── S01E02 - Cat's in the Bag.mp4
│   │   └── ...
│   └── Temporada 2/
│       ├── S02E01 - Seven Thirty-Seven.mp4
│       └── ...
└── Game of Thrones/
    ├── Temporada 1/
    └── Temporada 2/
```

## 🛠️ Requisitos del Sistema

### En Hostinger:
- ✅ PHP 7.4+ (incluido)
- ✅ wget (incluido)
- ✅ SSH/SFTP clients (incluido)
- ⚠️ sshpass (puede no estar disponible)

### En Servidor Remoto:
- ✅ SSH/SFTP habilitado
- ✅ Usuario con permisos de escritura
- ✅ Espacio suficiente para las series

## 🔍 Solución de Problemas

### Error: "No se pudo conectar al servidor remoto"
1. Verifica credenciales SFTP
2. Confirma que el puerto 22 esté abierto
3. Prueba la conexión SSH manualmente

### Error: "sshpass no disponible"
- El sistema usará métodos alternativos
- Contacta soporte de Hostinger si persiste

### Error: "No se pueden cargar directorios"
1. Verifica permisos del usuario en el servidor remoto
2. Intenta con rutas diferentes (/home/<USER>
3. Usa el botón "🔍 Buscar Rutas"

### Descarga lenta o falla
1. Verifica la URL del M3U
2. Comprueba la conexión de Hostinger
3. Revisa el log de descargas

## 📊 Monitoreo

### Ver Progreso en Tiempo Real
- Activa "▶️ Auto-actualizar" en el monitor
- Ve archivos descargándose uno por uno
- Controla con ⏸️ Pausar, ❌ Cancelar

### Logs de Sistema
- `download_output.log` - Log de descargas
- `download_progress.json` - Progreso actual

## 🔐 Seguridad

### Recomendaciones:
1. **No subas credenciales** al repositorio público
2. **Usa usuarios limitados** para SFTP
3. **Configura firewall** en el servidor remoto
4. **Cambia contraseñas** regularmente

### Archivos Sensibles:
- Las contraseñas se almacenan en sesión (temporal)
- Los archivos temporales se eliminan automáticamente
- Los logs no contienen credenciales

## 🆘 Soporte

### Contactos:
- **Hostinger**: Soporte técnico para herramientas del sistema
- **Servidor Remoto**: Administrador del servidor de destino

### Logs Útiles:
```bash
# En Hostinger
tail -f download_output.log

# En servidor remoto
tail -f /var/log/auth.log  # Conexiones SSH
```

## 🎯 Casos de Uso Típicos

### 1. Servidor Doméstico
- Hostinger descarga → Servidor casero via SFTP
- Ideal para colecciones personales

### 2. Servidor VPS
- Hostinger descarga → VPS remoto
- Para distribución o streaming

### 3. NAS/Media Server
- Hostinger descarga → Synology/QNAP
- Organización automática de medios

¡Tu sistema está listo para descargar y organizar series automáticamente!
