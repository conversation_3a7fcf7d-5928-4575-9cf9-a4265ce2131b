<?php
/**
 * Script para probar la navegación SFTP remota
 */

require_once 'sftp_alternative.php';

echo "=== PRUEBA DE NAVEGACIÓN SFTP REMOTA ===\n\n";

// Configuración de prueba (cambiar por tus datos reales)
$testConfig = [
    'host' => 'tu-servidor.com',
    'port' => 22,
    'username' => 'tu_usuario',
    'password' => 'tu_password'
];

echo "Configuración de prueba:\n";
echo "Host: {$testConfig['host']}\n";
echo "Puerto: {$testConfig['port']}\n";
echo "Usuario: {$testConfig['username']}\n";
echo "Contraseña: " . str_repeat('*', strlen($testConfig['password'])) . "\n\n";

echo "NOTA: Modifica las credenciales en test_sftp_navigation.php para probar con tu servidor real.\n\n";

// Probar conexión
echo "1. Probando conexión SFTP...\n";
try {
    if (extension_loaded('ssh2')) {
        echo "Usando extensión SSH2...\n";
        $connection = ssh2_connect($testConfig['host'], $testConfig['port']);
        if ($connection) {
            echo "✅ Conexión establecida\n";
            
            $auth = ssh2_auth_password($connection, $testConfig['username'], $testConfig['password']);
            if ($auth) {
                echo "✅ Autenticación exitosa\n";
                
                $sftp = ssh2_sftp($connection);
                if ($sftp) {
                    echo "✅ Sesión SFTP iniciada\n";
                    
                    // Probar navegación
                    echo "\n2. Probando navegación de directorios...\n";
                    
                    $testPaths = ['/', '/home', '/home/' . $testConfig['username'], '/var', '/opt'];
                    
                    foreach ($testPaths as $path) {
                        echo "Probando ruta: {$path}\n";
                        $handle = @opendir("ssh2.sftp://{$sftp}{$path}");
                        if ($handle) {
                            echo "  ✅ Accesible\n";
                            $count = 0;
                            while (($file = readdir($handle)) !== false && $count < 5) {
                                if ($file !== '.' && $file !== '..') {
                                    $type = @is_dir("ssh2.sftp://{$sftp}{$path}/{$file}") ? 'DIR' : 'FILE';
                                    echo "    {$type}: {$file}\n";
                                    $count++;
                                }
                            }
                            closedir($handle);
                            if ($count === 0) {
                                echo "    (directorio vacío)\n";
                            }
                        } else {
                            echo "  ❌ No accesible\n";
                        }
                    }
                    
                    ssh2_disconnect($connection);
                } else {
                    echo "❌ No se pudo inicializar SFTP\n";
                }
            } else {
                echo "❌ Falló la autenticación\n";
            }
        } else {
            echo "❌ No se pudo conectar\n";
        }
    } else {
        echo "Usando comandos del sistema...\n";
        $sftp = new SFTPAlternative(
            $testConfig['host'], 
            $testConfig['port'], 
            $testConfig['username'], 
            $testConfig['password']
        );
        
        $result = $sftp->testConnection();
        if ($result['success']) {
            echo "✅ " . $result['message'] . "\n";
            
            echo "\n2. Probando navegación de directorios...\n";
            $testPaths = ['/', '/home', '/home/' . $testConfig['username']];
            
            foreach ($testPaths as $path) {
                echo "Probando ruta: {$path}\n";
                $directories = $sftp->listDirectory($path);
                if ($directories !== false && !empty($directories)) {
                    echo "  ✅ Accesible (" . count($directories) . " elementos)\n";
                    foreach (array_slice($directories, 0, 5) as $item) {
                        echo "    {$item['type']}: {$item['name']}\n";
                    }
                } else {
                    echo "  ❌ No accesible o vacío\n";
                }
            }
        } else {
            echo "❌ " . $result['message'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== CONSEJOS PARA CONFIGURACIÓN ===\n\n";

echo "Si tienes problemas de conexión:\n";
echo "1. Verifica que el servidor SFTP esté corriendo\n";
echo "2. Confirma que el puerto 22 esté abierto\n";
echo "3. Asegúrate de que las credenciales sean correctas\n";
echo "4. Verifica que el usuario tenga permisos de SFTP\n\n";

echo "Si tienes problemas de navegación:\n";
echo "1. El usuario debe tener permisos de lectura en los directorios\n";
echo "2. Algunos servidores restringen el acceso a ciertos directorios\n";
echo "3. Intenta navegar desde el directorio home del usuario\n";
echo "4. Verifica la configuración del servidor SSH/SFTP\n\n";

echo "Rutas comunes para probar:\n";
echo "- /home/<USER>";
echo "- /var/www (archivos web)\n";
echo "- /opt (aplicaciones opcionales)\n";
echo "- /media (medios montados)\n";
echo "- /mnt (puntos de montaje)\n\n";

echo "¡Modifica las credenciales en este archivo y ejecuta la prueba!\n";
?>
