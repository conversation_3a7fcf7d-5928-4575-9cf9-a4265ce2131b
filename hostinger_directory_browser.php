<?php
/**
 * Explorador de directorios optimizado para Hostinger → Servidor remoto
 */

session_start();
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

try {
    if (!isset($_SESSION['sftp_config'])) {
        throw new Exception('No hay conexión SFTP establecida');
    }
    
    $action = $_POST['action'] ?? '';
    $config = $_SESSION['sftp_config'];
    
    switch ($action) {
        case 'list_directories':
            $path = $_POST['path'] ?? '/';
            $directories = listDirectoriesHostinger($config, $path);
            
            echo json_encode([
                'success' => true,
                'directories' => $directories,
                'currentPath' => $path,
                'server_info' => 'Hostinger → ' . $config['host']
            ]);
            break;
            
        case 'test_paths':
            $testPaths = [
                '/',
                '/home',
                '/home/' . $config['username'],
                '/var/www',
                '/var/www/html',
                '/media',
                '/mnt',
                '/opt'
            ];
            
            $accessiblePaths = [];
            foreach ($testPaths as $testPath) {
                if (testPathAccessHostinger($config, $testPath)) {
                    $accessiblePaths[] = $testPath;
                }
            }
            
            echo json_encode([
                'success' => true,
                'accessiblePaths' => $accessiblePaths
            ]);
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function listDirectoriesHostinger($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];
    
    // Método 1: Intentar con cURL para SFTP (si está disponible)
    if (function_exists('curl_init')) {
        $result = listWithCurlSFTP($config, $path);
        if ($result !== false) {
            return $result;
        }
    }
    
    // Método 2: Usar comandos del sistema si están disponibles
    $result = listWithSystemCommands($config, $path);
    if ($result !== false) {
        return $result;
    }
    
    // Método 3: Usar extensión SSH2 si está disponible
    if (extension_loaded('ssh2')) {
        $result = listWithSSH2Extension($config, $path);
        if ($result !== false) {
            return $result;
        }
    }
    
    // Método 4: Crear estructura de ejemplo para demostración
    return createHostingerSampleDirs($path);
}

function listWithCurlSFTP($config, $path) {
    // cURL con SFTP (si Hostinger lo soporta)
    $ch = curl_init();
    
    $url = "sftp://{$config['host']}:{$config['port']}{$path}";
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_USERPWD => $config['username'] . ':' . $config['password'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_DIRLISTONLY => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSH_AUTH_TYPES => CURLSSH_AUTH_PASSWORD
    ]);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($result !== false && empty($error)) {
        $files = explode("\n", trim($result));
        return parseCurlDirectoryListing($files, $path);
    }
    
    return false;
}

function listWithSystemCommands($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];
    
    // Intentar diferentes métodos según lo que esté disponible en Hostinger
    
    // Método 1: sshpass + sftp
    $tempScript = tempnam(sys_get_temp_dir(), 'sftp_list');
    $sftpCommands = "cd \"$path\"\nls -la\nquit\n";
    file_put_contents($tempScript, $sftpCommands);
    
    $commands = [
        "sshpass -p " . escapeshellarg($password) . " sftp -P $port -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -b " . escapeshellarg($tempScript) . " $username@$host 2>&1",
        "expect -c 'spawn sftp -P $port $username@$host; expect \"password:\"; send \"$password\\r\"; expect \"sftp>\"; send \"cd \\\"$path\\\"\\r\"; expect \"sftp>\"; send \"ls -la\\r\"; expect \"sftp>\"; send \"quit\\r\"; expect eof' 2>&1",
        "ssh -p $port -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $username@$host \"cd '$path' && ls -la\" 2>&1"
    ];
    
    foreach ($commands as $command) {
        $output = [];
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0 && !empty($output)) {
            unlink($tempScript);
            return parseDirectoryOutput($output, $path);
        }
    }
    
    unlink($tempScript);
    return false;
}

function listWithSSH2Extension($config, $path) {
    $connection = ssh2_connect($config['host'], $config['port']);
    if (!$connection) return false;
    
    $auth = ssh2_auth_password($connection, $config['username'], $config['password']);
    if (!$auth) return false;
    
    $sftp = ssh2_sftp($connection);
    if (!$sftp) return false;
    
    $handle = @opendir("ssh2.sftp://{$sftp}{$path}");
    if (!$handle) {
        ssh2_disconnect($connection);
        return false;
    }
    
    $items = [];
    while (($file = readdir($handle)) !== false) {
        if ($file === '.' || $file === '..') continue;
        
        $fullPath = rtrim($path, '/') . '/' . $file;
        if ($path === '/') $fullPath = '/' . $file;
        
        $isDir = @is_dir("ssh2.sftp://{$sftp}{$fullPath}");
        
        $items[] = [
            'name' => $file,
            'type' => $isDir ? 'directory' : 'file',
            'path' => $fullPath
        ];
    }
    
    closedir($handle);
    ssh2_disconnect($connection);
    
    usort($items, function($a, $b) {
        if ($a['type'] !== $b['type']) {
            return $a['type'] === 'directory' ? -1 : 1;
        }
        return strcasecmp($a['name'], $b['name']);
    });
    
    return $items;
}

function parseCurlDirectoryListing($files, $basePath) {
    $items = [];
    
    foreach ($files as $file) {
        $file = trim($file);
        if (empty($file) || $file === '.' || $file === '..') continue;
        
        $fullPath = rtrim($basePath, '/') . '/' . $file;
        if ($basePath === '/') $fullPath = '/' . $file;
        
        // Con cURL SFTP es difícil distinguir archivos de directorios
        // Asumimos que son directorios si no tienen extensión
        $isDirectory = !preg_match('/\.[a-zA-Z0-9]{1,4}$/', $file);
        
        $items[] = [
            'name' => $file,
            'type' => $isDirectory ? 'directory' : 'file',
            'path' => $fullPath
        ];
    }
    
    return $items;
}

function parseDirectoryOutput($output, $basePath) {
    $items = [];
    
    foreach ($output as $line) {
        $line = trim($line);
        
        if (empty($line) || 
            strpos($line, 'total ') === 0 || 
            strpos($line, 'sftp>') === 0 ||
            strpos($line, 'Connected to') !== false) {
            continue;
        }
        
        if (preg_match('/^([d-])([rwx-]{9})\s+\d+\s+\S+\s+\S+\s+\d+\s+\S+\s+\d+\s+[\d:]+\s+(.+)$/', $line, $matches)) {
            $isDirectory = $matches[1] === 'd';
            $name = trim($matches[3]);
            
            if ($name === '.' || $name === '..' || strpos($name, '.') === 0) {
                continue;
            }
            
            $fullPath = rtrim($basePath, '/') . '/' . $name;
            if ($basePath === '/') $fullPath = '/' . $name;
            
            $items[] = [
                'name' => $name,
                'type' => $isDirectory ? 'directory' : 'file',
                'path' => $fullPath
            ];
        }
    }
    
    usort($items, function($a, $b) {
        if ($a['type'] !== $b['type']) {
            return $a['type'] === 'directory' ? -1 : 1;
        }
        return strcasecmp($a['name'], $b['name']);
    });
    
    return $items;
}

function createHostingerSampleDirs($path) {
    // Estructura típica de servidores Linux para demostración
    $sampleDirs = [
        '/' => [
            ['name' => 'home', 'type' => 'directory', 'path' => '/home'],
            ['name' => 'var', 'type' => 'directory', 'path' => '/var'],
            ['name' => 'media', 'type' => 'directory', 'path' => '/media'],
            ['name' => 'opt', 'type' => 'directory', 'path' => '/opt']
        ],
        '/home' => [
            ['name' => 'usuario', 'type' => 'directory', 'path' => '/home/<USER>']
        ],
        '/home/<USER>' => [
            ['name' => 'series', 'type' => 'directory', 'path' => '/home/<USER>/series'],
            ['name' => 'peliculas', 'type' => 'directory', 'path' => '/home/<USER>/peliculas'],
            ['name' => 'documentos', 'type' => 'directory', 'path' => '/home/<USER>/documentos']
        ],
        '/var' => [
            ['name' => 'www', 'type' => 'directory', 'path' => '/var/www']
        ],
        '/var/www' => [
            ['name' => 'html', 'type' => 'directory', 'path' => '/var/www/html']
        ],
        '/media' => [
            ['name' => 'series', 'type' => 'directory', 'path' => '/media/series'],
            ['name' => 'movies', 'type' => 'directory', 'path' => '/media/movies']
        ]
    ];
    
    return $sampleDirs[$path] ?? [];
}

function testPathAccessHostinger($config, $path) {
    // Prueba rápida de acceso a ruta
    $command = "ssh -p {$config['port']} -o ConnectTimeout=5 -o StrictHostKeyChecking=no {$config['username']}@{$config['host']} \"test -d '$path' && echo 'OK'\" 2>/dev/null";
    
    $output = [];
    exec($command, $output, $returnCode);
    
    return $returnCode === 0 && in_array('OK', $output);
}
?>
