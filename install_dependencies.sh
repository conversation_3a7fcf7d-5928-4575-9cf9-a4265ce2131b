#!/bin/bash

echo "=== INSTALADOR DE DEPENDENCIAS PARA DESCARGADOR M3U ==="
echo ""

# Detectar sistema operativo
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    if command -v apt-get &> /dev/null; then
        # Debian/Ubuntu
        echo "🐧 Sistema detectado: Debian/Ubuntu"
        echo "Instalando dependencias..."
        
        sudo apt-get update
        sudo apt-get install -y php php-ssh2 php-curl php-json wget sshpass openssh-client
        
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        echo "🐧 Sistema detectado: CentOS/RHEL"
        echo "Instalando dependencias..."
        
        sudo yum install -y php php-ssh2 php-curl php-json wget sshpass openssh-clients
        
    elif command -v dnf &> /dev/null; then
        # Fedora
        echo "🐧 Sistema detectado: Fedora"
        echo "Instalando dependencias..."
        
        sudo dnf install -y php php-ssh2 php-curl php-json wget sshpass openssh-clients
        
    else
        echo "❌ Distribución de Linux no soportada automáticamente"
        echo "Por favor instala manualmente: php, php-ssh2, php-curl, wget, sshpass"
        exit 1
    fi
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "🍎 Sistema detectado: macOS"
    
    if command -v brew &> /dev/null; then
        echo "Instalando dependencias con Homebrew..."
        brew install php wget sshpass
        
        # Instalar extensión SSH2 para PHP
        pecl install ssh2
        
    else
        echo "❌ Homebrew no encontrado. Por favor instala Homebrew primero:"
        echo "https://brew.sh/"
        exit 1
    fi
    
else
    echo "❌ Sistema operativo no soportado: $OSTYPE"
    echo "Este script soporta Linux y macOS"
    exit 1
fi

echo ""
echo "=== VERIFICANDO INSTALACIÓN ==="

# Verificar PHP
if command -v php &> /dev/null; then
    echo "✅ PHP: $(php -v | head -n1)"
else
    echo "❌ PHP no encontrado"
    exit 1
fi

# Verificar extensiones PHP
echo "Verificando extensiones PHP..."

if php -m | grep -q ssh2; then
    echo "✅ Extensión SSH2 instalada"
else
    echo "❌ Extensión SSH2 no encontrada"
    echo "Intenta instalar con: sudo pecl install ssh2"
fi

if php -m | grep -q curl; then
    echo "✅ Extensión cURL instalada"
else
    echo "❌ Extensión cURL no encontrada"
fi

if php -m | grep -q json; then
    echo "✅ Extensión JSON instalada"
else
    echo "❌ Extensión JSON no encontrada"
fi

# Verificar herramientas del sistema
if command -v wget &> /dev/null; then
    echo "✅ wget: $(wget --version | head -n1)"
else
    echo "❌ wget no encontrado"
fi

if command -v sshpass &> /dev/null; then
    echo "✅ sshpass: $(sshpass -V 2>&1)"
else
    echo "❌ sshpass no encontrado"
fi

if command -v ssh &> /dev/null; then
    echo "✅ SSH client: $(ssh -V 2>&1 | head -n1)"
else
    echo "❌ SSH client no encontrado"
fi

if command -v scp &> /dev/null; then
    echo "✅ SCP disponible"
else
    echo "❌ SCP no encontrado"
fi

if command -v sftp &> /dev/null; then
    echo "✅ SFTP client disponible"
else
    echo "❌ SFTP client no encontrado"
fi

echo ""
echo "=== CONFIGURANDO PERMISOS ==="

# Crear directorios necesarios
mkdir -p downloads uploads
chmod 755 downloads uploads

# Hacer ejecutables los scripts
chmod +x download_script.sh 2>/dev/null || true
chmod +x check_requirements.php

echo "✅ Directorios y permisos configurados"

echo ""
echo "=== VERIFICACIÓN FINAL ==="

# Ejecutar verificación de requisitos
if [ -f "check_requirements.php" ]; then
    echo "Ejecutando verificación de requisitos..."
    php check_requirements.php
else
    echo "❌ Archivo check_requirements.php no encontrado"
fi

echo ""
echo "=== INSTALACIÓN COMPLETADA ==="
echo ""
echo "🎉 ¡Instalación completada!"
echo ""
echo "Próximos pasos:"
echo "1. Configura tu servidor SFTP en config.php"
echo "2. Accede a la interfaz web: http://tu-servidor/index.php"
echo "3. O ejecuta desde línea de comandos: php download_series.php archivo.m3u"
echo ""
echo "Para verificar el estado del sistema:"
echo "- Web: http://tu-servidor/status.php"
echo "- CLI: php check_requirements.php"
echo ""
