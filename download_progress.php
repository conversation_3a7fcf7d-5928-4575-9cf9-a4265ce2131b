<?php
/**
 * API para obtener el progreso de descarga en tiempo real
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$progressFile = './download_progress.json';

if (file_exists($progressFile)) {
    $progress = json_decode(file_get_contents($progressFile), true);
    
    // Agregar información adicional del log si está disponible
    $logFile = './download_output.log';
    if (file_exists($logFile)) {
        $logContent = file_get_contents($logFile);
        
        // Extraer información de wget del log
        $lines = explode("\n", $logContent);
        $lastLines = array_slice($lines, -10); // Últimas 10 líneas
        
        foreach ($lastLines as $line) {
            // Buscar líneas de progreso de wget
            if (preg_match('/(\d+)%.*?(\d+(?:\.\d+)?[KMG]?)\/s.*?eta\s+(\d+[hms]?)/', $line, $matches)) {
                $progress['percent'] = intval($matches[1]);
                $progress['speed'] = $matches[2] . '/s';
                $progress['timeRemaining'] = $matches[3];
            }
            
            // Buscar archivos completados
            if (strpos($line, '✅ Completado:') !== false) {
                $filename = trim(str_replace('✅ Completado:', '', $line));
                $progress['files'][] = [
                    'name' => $filename,
                    'status' => 'completed'
                ];
            }
            
            // Buscar errores
            if (strpos($line, '❌ Error') !== false) {
                $filename = trim(str_replace(['❌ Error descargando:', '❌ Error subiendo:'], '', $line));
                $progress['files'][] = [
                    'name' => $filename,
                    'status' => 'error'
                ];
            }
        }
    }
    
    echo json_encode($progress);
} else {
    echo json_encode([
        'percent' => 0,
        'currentFile' => 'No hay descarga activa',
        'speed' => '0 KB/s',
        'timeRemaining' => '--:--',
        'files' => [],
        'completed' => false,
        'error' => null
    ]);
}
?>
