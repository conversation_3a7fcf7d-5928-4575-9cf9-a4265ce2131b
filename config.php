<?php
/**
 * Configuración para el descargador de series M3U
 */

return [
    // Configuración del servidor SFTP
    'sftp' => [
        'host' => '***************',
        'port' => 22,
        'username' => 'root',
        'password' => 'oLX25iZXX3rDNaX',
        // O usar clave privada en lugar de password:
        // 'private_key' => '/path/to/private/key',
        // 'passphrase' => 'passphrase_if_needed',
    ],
    
    // Configuración de descarga
    'download' => [
        'base_directory' => './downloads/',  // Directorio base para descargas locales
        'remote_base_directory' => '/home/<USER>/series/', // Directorio base en servidor SFTP
        'timeout' => 300,                    // Timeout en segundos para descargas
        'max_retries' => 3,                  // Número máximo de reintentos
        'chunk_size' => 8192,                // Tamaño del chunk para descarga
        'use_wget' => true,                  // Usar wget para descargas
        'wget_options' => '--progress=bar:force --timeout=300 --tries=3', // Opciones de wget
    ],
    
    // Patrones para detectar series
    'patterns' => [
        'season_episode' => '/[Ss](\d+)[Ee](\d+)/',  // Patrón S01E01
        'season_episode_alt' => '/(\d+)x(\d+)/',      // Patrón 1x01
        'series_name' => '/^([^\/]+?)[\s\-\.]*[Ss]\d+[Ee]\d+/i',  // Extraer nombre de serie
    ],
    
    // Configuración de logging
    'logging' => [
        'enabled' => true,
        'file' => './download.log',
        'level' => 'INFO',  // DEBUG, INFO, WARNING, ERROR
    ]
];
