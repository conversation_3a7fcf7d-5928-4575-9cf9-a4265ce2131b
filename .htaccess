# Configuración para el Descargador de Series M3U

# Habilitar reescritura de URLs
RewriteEngine On

# Redirigir a index.php si no se especifica archivo
DirectoryIndex index.php

# Proteger archivos de configuración
<Files "config.php">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Proteger archivos de log
<Files "*.log">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Proteger directorios sensibles
<Directory "downloads">
    Options -Indexes
</Directory>

<Directory "uploads">
    Options -Indexes
</Directory>

# Permitir solo ciertos tipos de archivo en uploads
<Directory "uploads">
    <FilesMatch "\.(m3u|m3u8)$">
        Order Allow,Deny
        Allow from all
    </FilesMatch>
    <FilesMatch "^(?!.*\.(m3u|m3u8)$).*$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# Configuración de seguridad
<IfModule mod_headers.c>
    # Prevenir clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevenir MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Habilitar XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Configurar Content Security Policy básico
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
</IfModule>

# Configuración de cache para archivos estáticos
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>

# Configuración de compresión
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Aumentar límites para uploads grandes
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
php_value max_input_time 300
