<?php
/**
 * <PERSON>ript para verificar que todos los requisitos estén instalados
 */

echo "=== VERIFICACIÓN DE REQUISITOS ===\n\n";

$requirements = [
    'PHP Version' => [
        'check' => version_compare(PHP_VERSION, '7.0.0', '>='),
        'current' => PHP_VERSION,
        'required' => '7.0.0 o superior',
        'critical' => true
    ],
    'SSH2 Extension' => [
        'check' => extension_loaded('ssh2'),
        'current' => extension_loaded('ssh2') ? 'Instalada' : 'No instalada',
        'required' => 'Requerida para conexiones SFTP',
        'critical' => true
    ],
    'cURL Extension' => [
        'check' => extension_loaded('curl'),
        'current' => extension_loaded('curl') ? 'Instalada' : 'No instalada',
        'required' => 'Requerida para descargas HTTP',
        'critical' => true
    ],
    'OpenSSL Extension' => [
        'check' => extension_loaded('openssl'),
        'current' => extension_loaded('openssl') ? 'Instalada' : 'No instalada',
        'required' => 'Requerida para conexiones seguras',
        'critical' => false
    ],
    'JSON Extension' => [
        'check' => extension_loaded('json'),
        'current' => extension_loaded('json') ? 'Instalada' : 'No instalada',
        'required' => 'Requerida para configuración',
        'critical' => true
    ]
];

$allGood = true;
$criticalIssues = [];

foreach ($requirements as $name => $req) {
    $status = $req['check'] ? '✅ OK' : '❌ FALTA';
    echo sprintf("%-20s: %s\n", $name, $status);
    echo sprintf("%-20s  Actual: %s\n", '', $req['current']);
    echo sprintf("%-20s  Requerido: %s\n", '', $req['required']);
    echo "\n";
    
    if (!$req['check']) {
        $allGood = false;
        if ($req['critical']) {
            $criticalIssues[] = $name;
        }
    }
}

// Verificar archivos de configuración
echo "=== VERIFICACIÓN DE ARCHIVOS ===\n\n";

$files = [
    'config.php' => 'Archivo de configuración principal',
    'm3u_series_downloader.php' => 'Clase principal del descargador',
    'download_series.php' => 'Script principal de ejecución'
];

foreach ($files as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? '✅ Existe' : '❌ Falta';
    echo sprintf("%-30s: %s\n", $file, $status);
    echo sprintf("%-30s  %s\n", '', $description);
    echo "\n";
    
    if (!$exists) {
        $allGood = false;
        $criticalIssues[] = $file;
    }
}

// Verificar permisos de directorio
echo "=== VERIFICACIÓN DE PERMISOS ===\n\n";

$downloadDir = './downloads';
if (!is_dir($downloadDir)) {
    echo "Creando directorio de descargas: {$downloadDir}\n";
    if (mkdir($downloadDir, 0755, true)) {
        echo "✅ Directorio creado exitosamente\n";
    } else {
        echo "❌ No se pudo crear el directorio\n";
        $allGood = false;
        $criticalIssues[] = 'Permisos de escritura';
    }
} else {
    echo "✅ Directorio de descargas existe\n";
}

if (is_writable($downloadDir)) {
    echo "✅ Directorio de descargas es escribible\n";
} else {
    echo "❌ Directorio de descargas no es escribible\n";
    $allGood = false;
    $criticalIssues[] = 'Permisos de escritura';
}

echo "\n";

// Verificar configuración
echo "=== VERIFICACIÓN DE CONFIGURACIÓN ===\n\n";

if (file_exists('config.php')) {
    try {
        $config = require 'config.php';
        
        $configChecks = [
            'sftp.host' => 'Host del servidor SFTP',
            'sftp.username' => 'Usuario SFTP',
            'download.base_directory' => 'Directorio base de descargas'
        ];
        
        foreach ($configChecks as $key => $description) {
            $keys = explode('.', $key);
            $value = $config;
            foreach ($keys as $k) {
                $value = isset($value[$k]) ? $value[$k] : null;
            }
            
            if ($value && $value !== 'tu-servidor.com' && $value !== 'tu_usuario') {
                echo "✅ {$description}: Configurado\n";
            } else {
                echo "⚠️  {$description}: Necesita configuración\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error leyendo configuración: " . $e->getMessage() . "\n";
        $allGood = false;
    }
} else {
    echo "❌ Archivo config.php no encontrado\n";
}

echo "\n";

// Resumen final
echo "=== RESUMEN ===\n\n";

if ($allGood) {
    echo "🎉 ¡Todos los requisitos están satisfechos!\n";
    echo "Puedes ejecutar el script con:\n";
    echo "php download_series.php example.m3u\n\n";
} else {
    echo "❌ Se encontraron problemas que necesitan ser resueltos:\n\n";
    
    foreach ($criticalIssues as $issue) {
        echo "  - {$issue}\n";
    }
    
    echo "\n=== INSTRUCCIONES DE INSTALACIÓN ===\n\n";
    
    if (in_array('SSH2 Extension', $criticalIssues)) {
        echo "Para instalar SSH2:\n";
        echo "Ubuntu/Debian: sudo apt-get install php-ssh2\n";
        echo "CentOS/RHEL: sudo yum install php-ssh2\n";
        echo "Windows: Habilitar extension=ssh2 en php.ini\n\n";
    }
    
    if (in_array('cURL Extension', $criticalIssues)) {
        echo "Para instalar cURL:\n";
        echo "Ubuntu/Debian: sudo apt-get install php-curl\n";
        echo "CentOS/RHEL: sudo yum install php-curl\n";
        echo "Windows: Habilitar extension=curl en php.ini\n\n";
    }
    
    echo "Después de instalar las dependencias, ejecuta este script nuevamente.\n";
}

echo "\n";
?>
