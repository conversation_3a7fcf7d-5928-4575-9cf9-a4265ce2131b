<?php
/**
 * API para navegar directorios remotos via AJAX
 */

session_start();
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

require_once 'sftp_alternative.php';

// Función de logging para debug
function logDebug($message) {
    error_log("[BROWSE_DIR] " . $message);
}

try {
    logDebug("Iniciando navegación de directorio");

    if (!isset($_SESSION['sftp_config'])) {
        throw new Exception('No hay conexión SFTP establecida');
    }

    $browsePath = $_POST['browse_path'] ?? '/';
    $config = $_SESSION['sftp_config'];

    logDebug("Navegando a: {$browsePath}");
    logDebug("Servidor: {$config['host']}:{$config['port']}");

    // Normalizar la ruta remota
    $browsePath = rtrim($browsePath, '/');
    if (empty($browsePath)) $browsePath = '/';

    // Guardar la ruta actual en la sesión
    $_SESSION['current_browse_path'] = $browsePath;
    
    // Intentar usar SSH2 si está disponible
    if (extension_loaded('ssh2')) {
        logDebug("Usando extensión SSH2");
        $directories = listRemoteDirectoriesSSH2(
            $config['host'],
            $config['port'],
            $config['username'],
            $config['password'],
            $browsePath
        );
    } else {
        logDebug("Usando comandos del sistema");
        // Usar alternativa con comandos del sistema
        $sftp = new SFTPAlternative(
            $config['host'],
            $config['port'],
            $config['username'],
            $config['password']
        );
        $directories = $sftp->listDirectory($browsePath);
    }

    logDebug("Resultado del listado: " . ($directories === false ? "false" : count($directories) . " elementos"));
    
    if ($directories === false) {
        // Si no se puede acceder al directorio, intentar con alternativas
        $alternativePaths = [
            '/home/' . $config['username'],
            '/var/www',
            '/opt',
            '/usr/local'
        ];

        $foundPath = null;
        foreach ($alternativePaths as $altPath) {
            if ($altPath !== $browsePath) {
                $testDirs = listRemoteDirectoriesSSH2(
                    $config['host'],
                    $config['port'],
                    $config['username'],
                    $config['password'],
                    $altPath
                );
                if ($testDirs !== false) {
                    $directories = $testDirs;
                    $browsePath = $altPath;
                    $_SESSION['current_browse_path'] = $browsePath;
                    $foundPath = $altPath;
                    break;
                }
            }
        }

        if ($directories === false) {
            throw new Exception('No se pudo acceder al directorio: ' . $browsePath . '. Verifica los permisos.');
        }
    }

    echo json_encode([
        'success' => true,
        'directories' => $directories,
        'currentPath' => $browsePath,
        'message' => $foundPath ? "Redirigido a: {$foundPath}" : null
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function listRemoteDirectoriesSSH2($host, $port, $username, $password, $path = '/') {
    try {
        $connection = ssh2_connect($host, $port);
        if (!$connection) return false;
        
        $auth = ssh2_auth_password($connection, $username, $password);
        if (!$auth) return false;
        
        $sftp = ssh2_sftp($connection);
        if (!$sftp) return false;
        
        // Normalizar la ruta
        $path = rtrim($path, '/');
        if (empty($path)) $path = '/';
        
        $handle = @opendir("ssh2.sftp://{$sftp}{$path}");
        if (!$handle) {
            ssh2_disconnect($connection);
            return false;
        }
        
        $directories = [];
        $files = [];
        
        while (($file = readdir($handle)) !== false) {
            if ($file === '.' || $file === '..') continue;
            
            $fullPath = $path === '/' ? '/' . $file : $path . '/' . $file;
            if (@is_dir("ssh2.sftp://{$sftp}{$fullPath}")) {
                $directories[] = [
                    'name' => $file,
                    'path' => $fullPath,
                    'type' => 'directory'
                ];
            } else {
                $files[] = [
                    'name' => $file,
                    'path' => $fullPath,
                    'type' => 'file'
                ];
            }
        }
        
        closedir($handle);
        ssh2_disconnect($connection);
        
        // Ordenar directorios primero, luego archivos
        usort($directories, function($a, $b) { return strcasecmp($a['name'], $b['name']); });
        usort($files, function($a, $b) { return strcasecmp($a['name'], $b['name']); });
        
        return array_merge($directories, $files);
    } catch (Exception $e) {
        return false;
    }
}
?>
