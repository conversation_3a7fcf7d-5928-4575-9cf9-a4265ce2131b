<?php
/**
 * Script de diagnóstico para problemas de descarga
 */

session_start();
header('Content-Type: application/json');

echo json_encode([
    'debug' => true,
    'timestamp' => date('Y-m-d H:i:s'),
    'session_data' => [
        'sftp_config_exists' => isset($_SESSION['sftp_config']),
        'series_exists' => isset($_SESSION['series']),
        'download_config_exists' => isset($_SESSION['download_config']),
        'sftp_config' => isset($_SESSION['sftp_config']) ? [
            'host' => $_SESSION['sftp_config']['host'] ?? 'N/A',
            'port' => $_SESSION['sftp_config']['port'] ?? 'N/A',
            'username' => $_SESSION['sftp_config']['username'] ?? 'N/A',
            'password_set' => !empty($_SESSION['sftp_config']['password'])
        ] : null,
        'series_count' => isset($_SESSION['series']) ? count($_SESSION['series']) : 0,
        'download_config' => $_SESSION['download_config'] ?? null
    ],
    'system_info' => [
        'php_version' => PHP_VERSION,
        'os' => PHP_OS,
        'temp_dir' => sys_get_temp_dir(),
        'temp_writable' => is_writable(sys_get_temp_dir()),
        'current_dir' => getcwd(),
        'current_dir_writable' => is_writable('.'),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time')
    ],
    'commands_available' => [
        'wget' => commandExists('wget'),
        'curl' => commandExists('curl'),
        'ssh' => commandExists('ssh'),
        'scp' => commandExists('scp'),
        'sftp' => commandExists('sftp'),
        'sshpass' => commandExists('sshpass'),
        'bash' => commandExists('bash'),
        'sh' => commandExists('sh')
    ],
    'php_functions' => [
        'exec' => function_exists('exec'),
        'shell_exec' => function_exists('shell_exec'),
        'system' => function_exists('system'),
        'proc_open' => function_exists('proc_open'),
        'popen' => function_exists('popen'),
        'file_get_contents' => function_exists('file_get_contents'),
        'file_put_contents' => function_exists('file_put_contents'),
        'tempnam' => function_exists('tempnam')
    ],
    'file_permissions' => [
        'downloads_dir' => checkDirectory('./downloads'),
        'uploads_dir' => checkDirectory('./uploads'),
        'temp_dir' => checkDirectory('./temp'),
        'current_dir' => [
            'readable' => is_readable('.'),
            'writable' => is_writable('.'),
            'executable' => is_executable('.')
        ]
    ],
    'test_results' => [
        'temp_file_creation' => testTempFileCreation(),
        'exec_test' => testExecFunction(),
        'wget_test' => testWgetCommand()
    ]
], JSON_PRETTY_PRINT);

function commandExists($command) {
    $output = [];
    $returnCode = 0;
    
    if (PHP_OS_FAMILY === 'Windows') {
        exec("where $command 2>nul", $output, $returnCode);
    } else {
        exec("which $command 2>/dev/null", $output, $returnCode);
    }
    
    return [
        'available' => $returnCode === 0 && !empty($output),
        'path' => !empty($output) ? $output[0] : null,
        'return_code' => $returnCode
    ];
}

function checkDirectory($dir) {
    if (!is_dir($dir)) {
        @mkdir($dir, 0755, true);
    }
    
    return [
        'exists' => is_dir($dir),
        'readable' => is_readable($dir),
        'writable' => is_writable($dir),
        'path' => realpath($dir)
    ];
}

function testTempFileCreation() {
    try {
        $tempFile = tempnam(sys_get_temp_dir(), 'test_');
        if ($tempFile) {
            $testContent = 'Test content: ' . date('Y-m-d H:i:s');
            $writeResult = file_put_contents($tempFile, $testContent);
            $readResult = file_get_contents($tempFile);
            unlink($tempFile);
            
            return [
                'success' => true,
                'temp_file_created' => true,
                'write_success' => $writeResult !== false,
                'read_success' => $readResult === $testContent,
                'temp_file_path' => dirname($tempFile)
            ];
        } else {
            return [
                'success' => false,
                'error' => 'No se pudo crear archivo temporal'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

function testExecFunction() {
    try {
        $output = [];
        $returnCode = 0;
        
        if (PHP_OS_FAMILY === 'Windows') {
            exec('echo "Test exec function"', $output, $returnCode);
        } else {
            exec('echo "Test exec function"', $output, $returnCode);
        }
        
        return [
            'success' => $returnCode === 0,
            'output' => $output,
            'return_code' => $returnCode,
            'exec_available' => function_exists('exec')
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

function testWgetCommand() {
    try {
        $output = [];
        $returnCode = 0;
        
        // Probar wget con una URL simple
        $testUrl = 'https://httpbin.org/json';
        $tempFile = tempnam(sys_get_temp_dir(), 'wget_test');
        
        $command = "wget --timeout=5 --tries=1 -q -O " . escapeshellarg($tempFile) . " " . escapeshellarg($testUrl) . " 2>&1";
        
        exec($command, $output, $returnCode);
        
        $fileExists = file_exists($tempFile);
        $fileSize = $fileExists ? filesize($tempFile) : 0;
        
        if ($fileExists) {
            unlink($tempFile);
        }
        
        return [
            'command' => $command,
            'success' => $returnCode === 0 && $fileSize > 0,
            'return_code' => $returnCode,
            'output' => $output,
            'file_created' => $fileExists,
            'file_size' => $fileSize
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
?>
