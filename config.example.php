<?php
/**
 * Archivo de configuración de ejemplo para el descargador de series M3U
 * 
 * Copia este archivo como 'config.php' y modifica los valores según tu configuración
 */

return [
    // Configuración del servidor SFTP
    'sftp' => [
        'host' => 'tu-servidor.com',           // IP o dominio de tu servidor
        'port' => 22,                          // Puerto SSH (generalmente 22)
        'username' => 'tu_usuario',            // Usuario SSH/SFTP
        'password' => 'tu_password',           // Contraseña (o usar clave privada)
        
        // Alternativa: Autenticación con clave privada (más seguro)
        // 'private_key' => '/path/to/private/key',
        // 'public_key' => '/path/to/public/key',
        // 'passphrase' => 'passphrase_if_needed',
        
        // Directorio base en el servidor remoto (opcional)
        'remote_base_path' => '/home/<USER>/media/',
    ],
    
    // Configuración de descarga
    'download' => [
        'base_directory' => './downloads/',    // Directorio local para descargas
        'timeout' => 300,                      // Timeout en segundos para descargas (5 minutos)
        'max_retries' => 3,                    // Número máximo de reintentos por archivo
        'chunk_size' => 8192,                  // Tamaño del chunk para descarga (8KB)
        'concurrent_downloads' => 1,           // Descargas simultáneas (experimental)
        'verify_ssl' => true,                  // Verificar certificados SSL para HTTPS
        'user_agent' => 'M3U Series Downloader 1.0',  // User agent para descargas HTTP
    ],
    
    // Patrones para detectar series y episodios
    'patterns' => [
        // Patrón principal: S01E01, S1E1, etc.
        'season_episode' => '/[Ss](\d+)[Ee](\d+)/',
        
        // Patrón alternativo: 1x01, 2x05, etc.
        'season_episode_alt' => '/(\d+)x(\d+)/',
        
        // Patrón para extraer nombre de serie
        'series_name' => '/^([^\/]+?)[\s\-\.]*[Ss]\d+[Ee]\d+/i',
        
        // Patrones adicionales personalizados
        'custom_patterns' => [
            // Ejemplo: "Temporada 1 Episodio 5"
            '/Temporada[\s]*(\d+)[\s]*Episodio[\s]*(\d+)/i',
            // Ejemplo: "Season 1 Episode 5"
            '/Season[\s]*(\d+)[\s]*Episode[\s]*(\d+)/i',
        ],
    ],
    
    // Configuración de organización de archivos
    'organization' => [
        // Formato de nombre de carpeta de serie
        'series_folder_format' => '{series_name}',
        
        // Formato de nombre de carpeta de temporada
        'season_folder_format' => 'Temporada {season_number}',
        
        // Formato de nombre de archivo de episodio
        'episode_file_format' => 'S{season:02d}E{episode:02d} - {episode_title}.{extension}',
        
        // Caracteres a remover/reemplazar en nombres de archivo
        'invalid_chars' => ['<', '>', ':', '"', '/', '\\', '|', '?', '*'],
        'replacement_char' => '_',
        
        // Longitud máxima de nombres de archivo
        'max_filename_length' => 200,
        
        // Crear enlaces simbólicos además de copiar archivos
        'create_symlinks' => false,
    ],
    
    // Configuración de logging
    'logging' => [
        'enabled' => true,                     // Habilitar logging
        'file' => './download.log',            // Archivo de log
        'level' => 'INFO',                     // Nivel de log: DEBUG, INFO, WARNING, ERROR
        'max_size' => 10485760,                // Tamaño máximo del log (10MB)
        'rotate' => true,                      // Rotar logs cuando alcancen el tamaño máximo
        'date_format' => 'Y-m-d H:i:s',        // Formato de fecha en logs
    ],
    
    // Configuración de la interfaz web
    'web' => [
        'max_upload_size' => 52428800,         // Tamaño máximo de upload (50MB)
        'allowed_extensions' => ['m3u', 'm3u8'], // Extensiones permitidas
        'session_timeout' => 3600,             // Timeout de sesión (1 hora)
        'auto_refresh_log' => 5,               // Auto-refresh del log cada X segundos
    ],
    
    // Configuración de notificaciones (opcional)
    'notifications' => [
        'enabled' => false,                    // Habilitar notificaciones
        'email' => [
            'enabled' => false,
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'tu_password_app',
            'from_email' => '<EMAIL>',
            'to_email' => '<EMAIL>',
        ],
        'webhook' => [
            'enabled' => false,
            'url' => 'https://hooks.slack.com/services/...',  // Webhook de Slack, Discord, etc.
        ],
    ],
    
    // Configuración de filtros
    'filters' => [
        // Filtrar por calidad de video
        'quality_filter' => [
            'enabled' => false,
            'preferred_qualities' => ['1080p', '720p', '480p'],  // Orden de preferencia
        ],
        
        // Filtrar por idioma
        'language_filter' => [
            'enabled' => false,
            'preferred_languages' => ['es', 'en'],  // Códigos de idioma preferidos
        ],
        
        // Excluir ciertos archivos
        'exclude_patterns' => [
            '/trailer/i',                      // Excluir trailers
            '/sample/i',                       // Excluir samples
            '/preview/i',                      // Excluir previews
        ],
    ],
    
    // Configuración de base de datos (opcional, para tracking)
    'database' => [
        'enabled' => false,                    // Habilitar base de datos
        'type' => 'sqlite',                    // sqlite, mysql, postgresql
        'file' => './downloads.db',            // Para SQLite
        // Para MySQL/PostgreSQL:
        // 'host' => 'localhost',
        // 'port' => 3306,
        // 'database' => 'series_downloader',
        // 'username' => 'db_user',
        // 'password' => 'db_password',
    ],
    
    // Configuración de seguridad
    'security' => [
        'allowed_hosts' => [],                 // Lista de hosts permitidos (vacío = todos)
        'max_concurrent_sessions' => 5,        // Máximo de sesiones concurrentes
        'rate_limit' => [
            'enabled' => false,
            'max_requests' => 100,             // Máximo de requests por ventana
            'window_minutes' => 60,            // Ventana de tiempo en minutos
        ],
    ],
];
