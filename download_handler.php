<?php
/**
 * Manejador de descargas con wget y SFTP
 */

session_start();
header('Content-Type: application/json');

require_once 'm3u_series_downloader.php';

class DownloadHandler {
    private $progressFile = './download_progress.json';
    private $pidFile = './download.pid';
    
    public function __construct() {
        // Crear archivos de progreso si no existen
        if (!file_exists($this->progressFile)) {
            $this->saveProgress([
                'percent' => 0,
                'currentFile' => '',
                'speed' => '0 KB/s',
                'timeRemaining' => '--:--',
                'files' => [],
                'completed' => false,
                'error' => null
            ]);
        }
    }
    
    public function executeDownload() {
        try {
            if (!isset($_SESSION['sftp_config']) || !isset($_SESSION['series']) || !isset($_SESSION['download_config'])) {
                throw new Exception('Configuración incompleta');
            }
            
            $sftpConfig = $_SESSION['sftp_config'];
            $series = $_SESSION['series'];
            $downloadConfig = $_SESSION['download_config'];
            
            // Crear script de descarga
            $scriptPath = $this->createDownloadScript($sftpConfig, $series, $downloadConfig);
            
            // Ejecutar en segundo plano
            $command = "bash {$scriptPath} > download_output.log 2>&1 & echo $!";
            $pid = exec($command);
            
            // Guardar PID
            file_put_contents($this->pidFile, $pid);
            
            return ['success' => true, 'pid' => $pid];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    private function createDownloadScript($sftpConfig, $series, $downloadConfig) {
        $scriptPath = './download_script.sh';
        $downloadPath = $downloadConfig['download_path'];
        $selectedSeries = $downloadConfig['selected_series'];
        
        // Preparar lista de archivos a descargar
        $filesToDownload = [];
        
        foreach ($series as $seriesName => $seasons) {
            if ($selectedSeries && $selectedSeries !== $seriesName) {
                continue;
            }
            
            foreach ($seasons as $seasonNum => $episodes) {
                foreach ($episodes as $episodeNum => $entry) {
                    $url = $entry['url'];
                    $title = $entry['title'];
                    
                    // Generar nombre de archivo
                    $extension = $this->getFileExtension($url);
                    $filename = sprintf("S%02dE%02d - %s.%s", 
                        $seasonNum, 
                        $episodeNum, 
                        $this->cleanFileName($title), 
                        $extension
                    );
                    
                    // Crear estructura de directorios
                    $seriesDir = $downloadPath . '/' . $this->cleanFileName($seriesName);
                    $seasonDir = $seriesDir . '/Temporada ' . $seasonNum;
                    
                    $filesToDownload[] = [
                        'url' => $url,
                        'filename' => $filename,
                        'local_path' => $seasonDir . '/' . $filename,
                        'series' => $seriesName,
                        'season' => $seasonNum,
                        'episode' => $episodeNum
                    ];
                }
            }
        }
        
        // Crear script bash
        $script = $this->generateBashScript($sftpConfig, $filesToDownload);
        file_put_contents($scriptPath, $script);
        chmod($scriptPath, 0755);
        
        return $scriptPath;
    }
    
    private function generateBashScript($sftpConfig, $files) {
        $host = $sftpConfig['host'];
        $port = $sftpConfig['port'];
        $username = $sftpConfig['username'];
        $password = $sftpConfig['password'];
        
        $script = "#!/bin/bash\n\n";
        $script .= "# Script de descarga generado automáticamente\n";
        $script .= "# Host: {$host}:{$port}\n";
        $script .= "# Usuario: {$username}\n\n";
        
        $script .= "PROGRESS_FILE='" . $this->progressFile . "'\n";
        $script .= "TOTAL_FILES=" . count($files) . "\n";
        $script .= "CURRENT_FILE=0\n\n";
        
        $script .= "# Función para actualizar progreso\n";
        $script .= "update_progress() {\n";
        $script .= "    local percent=\$(( CURRENT_FILE * 100 / TOTAL_FILES ))\n";
        $script .= "    local current_file=\"\$1\"\n";
        $script .= "    local status=\"\$2\"\n";
        $script .= "    \n";
        $script .= "    cat > \$PROGRESS_FILE << EOF\n";
        $script .= "{\n";
        $script .= "    \"percent\": \$percent,\n";
        $script .= "    \"currentFile\": \"\$current_file\",\n";
        $script .= "    \"speed\": \"0 KB/s\",\n";
        $script .= "    \"timeRemaining\": \"--:--\",\n";
        $script .= "    \"completed\": false,\n";
        $script .= "    \"files\": []\n";
        $script .= "}\n";
        $script .= "EOF\n";
        $script .= "}\n\n";
        
        $script .= "# Función para crear directorio en SFTP\n";
        $script .= "create_sftp_dir() {\n";
        $script .= "    local dir=\"\$1\"\n";
        $script .= "    sshpass -p '{$password}' sftp -P {$port} -o StrictHostKeyChecking=no {$username}@{$host} << EOF\n";
        $script .= "mkdir \"\$dir\"\n";
        $script .= "quit\n";
        $script .= "EOF\n";
        $script .= "}\n\n";
        
        $script .= "# Función para subir archivo via SFTP\n";
        $script .= "upload_file() {\n";
        $script .= "    local local_file=\"\$1\"\n";
        $script .= "    local remote_file=\"\$2\"\n";
        $script .= "    \n";
        $script .= "    sshpass -p '{$password}' scp -P {$port} -o StrictHostKeyChecking=no \"\$local_file\" {$username}@{$host}:\"\$remote_file\"\n";
        $script .= "}\n\n";
        
        $script .= "echo \"Iniciando descarga de \$TOTAL_FILES archivos...\"\n\n";
        
        // Crear directorios únicos
        $directories = [];
        foreach ($files as $file) {
            $dir = dirname($file['local_path']);
            if (!in_array($dir, $directories)) {
                $directories[] = $dir;
            }
        }
        
        foreach ($directories as $dir) {
            $script .= "echo \"Creando directorio: {$dir}\"\n";
            $script .= "create_sftp_dir \"{$dir}\"\n\n";
        }
        
        // Descargar archivos
        foreach ($files as $index => $file) {
            $localTempFile = sys_get_temp_dir() . DIRECTORY_SEPARATOR . "download_" . basename($file['filename']);

            $script .= "# Archivo " . ($index + 1) . " de " . count($files) . ": {$file['filename']}\n";
            $script .= "CURRENT_FILE=" . ($index + 1) . "\n";
            $script .= "update_progress \"{$file['filename']}\" \"downloading\"\n";
            $script .= "echo \"[" . date('Y-m-d H:i:s') . "] Descargando: {$file['filename']}\"\n";

            // Usar wget para descargar con progreso detallado
            $wgetOptions = "--progress=bar:force:noscroll --timeout=300 --tries=3 --continue --user-agent='M3U-Downloader/1.0'";
            $script .= "wget {$wgetOptions} -O \"{$localTempFile}\" \"{$file['url']}\" 2>&1 | while IFS= read -r line; do\n";
            $script .= "    echo \"[WGET] \$line\"\n";
            $script .= "    # Extraer progreso de wget si es posible\n";
            $script .= "    if echo \"\$line\" | grep -q '%'; then\n";
            $script .= "        echo \"[PROGRESS] \$line\"\n";
            $script .= "    fi\n";
            $script .= "done\n";

            $script .= "WGET_EXIT=\$?\n";
            $script .= "if [ \$WGET_EXIT -eq 0 ]; then\n";
            $script .= "    echo \"[" . date('Y-m-d H:i:s') . "] ✅ Descarga completada: {$file['filename']}\"\n";
            $script .= "    echo \"[" . date('Y-m-d H:i:s') . "] 📤 Subiendo a servidor SFTP...\"\n";
            $script .= "    upload_file \"{$localTempFile}\" \"{$file['local_path']}\"\n";
            $script .= "    UPLOAD_EXIT=\$?\n";
            $script .= "    if [ \$UPLOAD_EXIT -eq 0 ]; then\n";
            $script .= "        echo \"[" . date('Y-m-d H:i:s') . "] ✅ Subida completada: {$file['filename']}\"\n";
            $script .= "        rm \"{$localTempFile}\"\n";
            $script .= "    else\n";
            $script .= "        echo \"[" . date('Y-m-d H:i:s') . "] ❌ Error subiendo: {$file['filename']}\"\n";
            $script .= "    fi\n";
            $script .= "else\n";
            $script .= "    echo \"[" . date('Y-m-d H:i:s') . "] ❌ Error descargando: {$file['filename']} (código: \$WGET_EXIT)\"\n";
            $script .= "fi\n\n";
        }
        
        $script .= "# Marcar como completado\n";
        $script .= "cat > \$PROGRESS_FILE << EOF\n";
        $script .= "{\n";
        $script .= "    \"percent\": 100,\n";
        $script .= "    \"currentFile\": \"Descarga completada\",\n";
        $script .= "    \"speed\": \"0 KB/s\",\n";
        $script .= "    \"timeRemaining\": \"00:00\",\n";
        $script .= "    \"completed\": true,\n";
        $script .= "    \"files\": []\n";
        $script .= "}\n";
        $script .= "EOF\n\n";
        
        $script .= "echo \"🎉 Descarga completada!\"\n";
        
        return $script;
    }
    
    private function getFileExtension($url) {
        $path = parse_url($url, PHP_URL_PATH);
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        return $extension ?: 'mp4';
    }
    
    private function cleanFileName($name) {
        $name = preg_replace('/[<>:"/\\|?*]/', '', $name);
        $name = trim($name, '. ');
        return substr($name, 0, 100);
    }
    
    public function pauseDownload() {
        if (file_exists($this->pidFile)) {
            $pid = file_get_contents($this->pidFile);
            if (PHP_OS_FAMILY === 'Windows') {
                // En Windows usar taskkill
                exec("taskkill /PID {$pid} /F");
            } else {
                exec("kill -STOP {$pid}");
            }

            // Marcar como pausado
            file_put_contents('./download_status.txt', 'paused');
            return ['success' => true, 'message' => 'Descarga pausada'];
        }
        return ['success' => false, 'message' => 'No hay descarga activa'];
    }

    public function resumeDownload() {
        if (file_exists($this->pidFile)) {
            $pid = file_get_contents($this->pidFile);
            if (PHP_OS_FAMILY === 'Windows') {
                // En Windows, reiniciar el proceso
                return $this->executeDownload();
            } else {
                exec("kill -CONT {$pid}");
            }

            // Marcar como activo
            file_put_contents('./download_status.txt', 'active');
            return ['success' => true, 'message' => 'Descarga reanudada'];
        }
        return ['success' => false, 'message' => 'No hay descarga activa'];
    }

    public function cancelDownload() {
        if (file_exists($this->pidFile)) {
            $pid = file_get_contents($this->pidFile);

            if (PHP_OS_FAMILY === 'Windows') {
                exec("taskkill /PID {$pid} /F");
            } else {
                exec("kill -TERM {$pid}");
            }

            unlink($this->pidFile);

            // Limpiar archivos temporales
            $this->saveProgress([
                'percent' => 0,
                'currentFile' => 'Descarga cancelada',
                'completed' => true,
                'error' => 'Cancelado por el usuario'
            ]);

            // Marcar como cancelado
            file_put_contents('./download_status.txt', 'cancelled');

            return ['success' => true, 'message' => 'Descarga cancelada'];
        }
        return ['success' => false, 'message' => 'No hay descarga activa'];
    }
    
    private function saveProgress($data) {
        file_put_contents($this->progressFile, json_encode($data, JSON_PRETTY_PRINT));
    }
}

// Procesar solicitud
$handler = new DownloadHandler();
$action = $_POST['action'] ?? '';

switch ($action) {
    case 'execute_download':
        echo json_encode($handler->executeDownload());
        break;

    case 'pause':
        echo json_encode($handler->pauseDownload());
        break;

    case 'resume':
        echo json_encode($handler->resumeDownload());
        break;

    case 'cancel':
        echo json_encode($handler->cancelDownload());
        break;

    default:
        echo json_encode(['success' => false, 'message' => 'Acción no válida']);
}
?>
