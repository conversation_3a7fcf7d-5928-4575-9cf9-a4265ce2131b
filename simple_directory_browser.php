<?php
/**
 * Explorador de directorios simplificado que funciona sin SSH2
 */

session_start();
header('Content-Type: application/json');

try {
    if (!isset($_SESSION['sftp_config'])) {
        throw new Exception('No hay conexión SFTP establecida');
    }
    
    $action = $_POST['action'] ?? '';
    $config = $_SESSION['sftp_config'];
    
    switch ($action) {
        case 'list_directories':
            $path = $_POST['path'] ?? '/';
            $directories = listDirectoriesSimple($config, $path);
            
            echo json_encode([
                'success' => true,
                'directories' => $directories,
                'currentPath' => $path
            ]);
            break;
            
        case 'test_paths':
            $testPaths = [
                '/',
                '/home',
                '/home/' . $config['username'],
                '/var',
                '/opt',
                '/media',
                '/mnt'
            ];
            
            $accessiblePaths = [];
            foreach ($testPaths as $testPath) {
                $result = testPathAccess($config, $testPath);
                if ($result) {
                    $accessiblePaths[] = $testPath;
                }
            }
            
            echo json_encode([
                'success' => true,
                'accessiblePaths' => $accessiblePaths
            ]);
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function listDirectoriesSimple($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Desde Hostinger (Linux) conectando a servidor remoto (Linux)
    return listDirectoriesLinuxToLinux($config, $path);
}

function listDirectoriesWindows($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Método 1: Intentar con OpenSSH nativo de Windows 10+
    if (commandExists('ssh')) {
        $result = listWithOpenSSH($config, $path);
        if ($result !== false) {
            return $result;
        }
    }

    // Método 2: Intentar con PuTTY plink
    if (commandExists('plink')) {
        $result = listWithPlink($config, $path);
        if ($result !== false) {
            return $result;
        }
    }

    // Método 3: Intentar con PowerShell y SSH
    $result = listWithPowerShell($config, $path);
    if ($result !== false) {
        return $result;
    }

    // Si nada funciona, mostrar mensaje de error útil
    throw new Exception("No se pudo conectar al servidor Linux. Instala OpenSSH: 'winget install Microsoft.OpenSSH.Beta' o PuTTY: 'winget install PuTTY.PuTTY'");
}

function commandExists($command) {
    $output = [];
    $returnCode = 0;
    exec("where $command 2>nul", $output, $returnCode);
    return $returnCode === 0;
}

function listWithOpenSSH($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Método optimizado para Windows con Git SSH
    // Usar SSH directo para ejecutar ls en el servidor Linux
    $command = "ssh -p $port -o ConnectTimeout=10 -o StrictHostKeyChecking=no -o PasswordAuthentication=yes $username@$host \"cd '$path' && ls -la\" 2>&1";

    // Crear un script temporal para manejar la contraseña
    $expectScript = tempnam(sys_get_temp_dir(), 'ssh_expect') . '.bat';

    $batContent = "@echo off\n";
    $batContent .= "echo $password | $command\n";

    file_put_contents($expectScript, $batContent);

    $output = [];
    exec("cmd /c \"$expectScript\"", $output, $returnCode);

    unlink($expectScript);

    if ($returnCode === 0 && !empty($output)) {
        return parseDirectoryOutput($output, $path);
    }

    // Método alternativo con SFTP
    return listWithSFTPBatch($config, $path);
}

function listWithSFTPBatch($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Crear script SFTP temporal
    $tempScript = tempnam(sys_get_temp_dir(), 'sftp_list');
    $sftpCommands = "cd \"$path\"\nls -la\nquit\n";
    file_put_contents($tempScript, $sftpCommands);

    // Crear script batch para manejar la contraseña
    $batScript = tempnam(sys_get_temp_dir(), 'sftp_batch') . '.bat';
    $batContent = "@echo off\n";
    $batContent .= "echo $password | sftp -P $port -o StrictHostKeyChecking=no -b \"$tempScript\" $username@$host\n";

    file_put_contents($batScript, $batContent);

    $output = [];
    exec("cmd /c \"$batScript\" 2>&1", $output, $returnCode);

    unlink($tempScript);
    unlink($batScript);

    if ($returnCode === 0 && !empty($output)) {
        return parseDirectoryOutput($output, $path);
    }

    return false;
}

function listWithPlink($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    $tempScript = tempnam(sys_get_temp_dir(), 'sftp_list');
    $sftpCommands = "cd \"$path\"\nls -la\nquit\n";
    file_put_contents($tempScript, $sftpCommands);

    // Usar plink para SFTP
    $command = "echo y | plink -sftp -P $port -l $username -pw \"$password\" $host -m \"$tempScript\" 2>&1";

    $output = [];
    exec($command, $output, $returnCode);
    unlink($tempScript);

    if ($returnCode === 0 && !empty($output)) {
        return parseDirectoryOutput($output, $path);
    }

    return false;
}

function listWithPowerShell($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Crear script PowerShell para conectar via SSH
    $psScript = tempnam(sys_get_temp_dir(), 'ssh_list') . '.ps1';

    $psContent = "
\$securePassword = ConvertTo-SecureString '$password' -AsPlainText -Force
\$credential = New-Object System.Management.Automation.PSCredential('$username', \$securePassword)

try {
    # Intentar ejecutar comando ls en el servidor remoto
    \$result = ssh -p $port -o StrictHostKeyChecking=no $username@$host \"cd '$path' && ls -la\" 2>&1
    Write-Output \$result
} catch {
    Write-Error \$_.Exception.Message
}
";

    file_put_contents($psScript, $psContent);

    $command = "powershell -ExecutionPolicy Bypass -File \"$psScript\" 2>&1";

    $output = [];
    exec($command, $output, $returnCode);

    unlink($psScript);

    if ($returnCode === 0 && !empty($output)) {
        return parseDirectoryOutput($output, $path);
    }

    return false;
}

function listDirectoriesLinuxToLinux($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Método 1: Intentar con sshpass (más confiable)
    if (commandExistsLinux('sshpass')) {
        $result = listWithSSHPass($config, $path);
        if ($result !== false) {
            return $result;
        }
    }

    // Método 2: Intentar con expect (si está disponible)
    if (commandExistsLinux('expect')) {
        $result = listWithExpect($config, $path);
        if ($result !== false) {
            return $result;
        }
    }

    // Método 3: SFTP básico (puede requerir interacción)
    $result = listWithBasicSFTP($config, $path);
    if ($result !== false) {
        return $result;
    }

    throw new Exception("No se pudo conectar al servidor remoto. Verifica que sshpass esté instalado en Hostinger o contacta soporte.");
}

function commandExistsLinux($command) {
    $output = [];
    $returnCode = 0;
    exec("which $command 2>/dev/null", $output, $returnCode);
    return $returnCode === 0 && !empty($output);
}

function listWithSSHPass($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Crear script SFTP temporal
    $tempScript = tempnam(sys_get_temp_dir(), 'sftp_list');
    $sftpCommands = "cd \"$path\"\nls -la\nquit\n";
    file_put_contents($tempScript, $sftpCommands);

    // Usar sshpass para autenticación automática
    $command = "sshpass -p " . escapeshellarg($password) . " sftp -P $port -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -b " . escapeshellarg($tempScript) . " $username@$host 2>&1";

    $output = [];
    exec($command, $output, $returnCode);
    unlink($tempScript);

    if ($returnCode === 0 && !empty($output)) {
        return parseDirectoryOutput($output, $path);
    }

    return false;
}

function listWithExpect($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Crear script expect temporal
    $expectScript = tempnam(sys_get_temp_dir(), 'expect_sftp');

    $expectContent = "#!/usr/bin/expect -f
set timeout 30
spawn sftp -P $port -o StrictHostKeyChecking=no $username@$host
expect \"password:\"
send \"$password\\r\"
expect \"sftp>\"
send \"cd \\\"$path\\\"\\r\"
expect \"sftp>\"
send \"ls -la\\r\"
expect \"sftp>\"
send \"quit\\r\"
expect eof
";

    file_put_contents($expectScript, $expectContent);
    chmod($expectScript, 0755);

    $output = [];
    exec($expectScript . " 2>&1", $output, $returnCode);
    unlink($expectScript);

    if ($returnCode === 0 && !empty($output)) {
        return parseDirectoryOutput($output, $path);
    }

    return false;
}

function listWithBasicSFTP($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Intentar SSH directo para listar directorio
    $command = "ssh -p $port -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o PasswordAuthentication=yes $username@$host \"cd '$path' && ls -la\" 2>&1";

    $output = [];
    exec($command, $output, $returnCode);

    if ($returnCode === 0 && !empty($output)) {
        return parseDirectoryOutput($output, $path);
    }

    return false;
}

function createSampleDirectories($path) {
    // Crear estructura de directorios de ejemplo para demostración
    $sampleDirs = [
        '/' => [
            ['name' => 'home', 'type' => 'directory', 'path' => '/home'],
            ['name' => 'var', 'type' => 'directory', 'path' => '/var'],
            ['name' => 'opt', 'type' => 'directory', 'path' => '/opt'],
            ['name' => 'media', 'type' => 'directory', 'path' => '/media'],
            ['name' => 'mnt', 'type' => 'directory', 'path' => '/mnt']
        ],
        '/home' => [
            ['name' => 'usuario', 'type' => 'directory', 'path' => '/home/<USER>'],
            ['name' => 'admin', 'type' => 'directory', 'path' => '/home/<USER>']
        ],
        '/home/<USER>' => [
            ['name' => 'documentos', 'type' => 'directory', 'path' => '/home/<USER>/documentos'],
            ['name' => 'descargas', 'type' => 'directory', 'path' => '/home/<USER>/descargas'],
            ['name' => 'media', 'type' => 'directory', 'path' => '/home/<USER>/media'],
            ['name' => 'series', 'type' => 'directory', 'path' => '/home/<USER>/series'],
            ['name' => 'peliculas', 'type' => 'directory', 'path' => '/home/<USER>/peliculas']
        ],
        '/var' => [
            ['name' => 'www', 'type' => 'directory', 'path' => '/var/www'],
            ['name' => 'log', 'type' => 'directory', 'path' => '/var/log']
        ],
        '/media' => [
            ['name' => 'series', 'type' => 'directory', 'path' => '/media/series'],
            ['name' => 'movies', 'type' => 'directory', 'path' => '/media/movies'],
            ['name' => 'music', 'type' => 'directory', 'path' => '/media/music']
        ]
    ];

    return $sampleDirs[$path] ?? [];
}

function parseDirectoryOutput($output, $basePath) {
    $items = [];

    foreach ($output as $line) {
        $line = trim($line);

        // Saltar líneas vacías y de información
        if (empty($line) ||
            strpos($line, 'total ') === 0 ||
            strpos($line, 'sftp>') === 0 ||
            strpos($line, 'Connected to') !== false ||
            strpos($line, 'Changing to:') !== false) {
            continue;
        }

        // Patrón para ls -la en sistemas Linux/Unix
        if (preg_match('/^([d-])([rwx-]{9})\s+\d+\s+\S+\s+\S+\s+\d+\s+(\S+\s+\d+\s+[\d:]+)\s+(.+)$/', $line, $matches)) {
            $isDirectory = $matches[1] === 'd';
            $name = trim($matches[4]);

            // Saltar . y .. y archivos ocultos que empiecen con .
            if ($name === '.' || $name === '..' || (strpos($name, '.') === 0 && strlen($name) > 1)) {
                continue;
            }

            // Construir ruta completa en formato Linux
            $fullPath = rtrim($basePath, '/') . '/' . $name;
            if ($basePath === '/') {
                $fullPath = '/' . $name;
            }

            $items[] = [
                'name' => $name,
                'type' => $isDirectory ? 'directory' : 'file',
                'path' => $fullPath,
                'permissions' => $matches[1] . $matches[2],
                'date' => $matches[3]
            ];
        }
        // Patrón alternativo más simple para algunos sistemas
        else if (preg_match('/^([d-])[rwx-]{9}\s+.*\s+(.+)$/', $line, $matches)) {
            $isDirectory = $matches[1] === 'd';
            $name = trim($matches[2]);

            if ($name === '.' || $name === '..' || empty($name)) {
                continue;
            }

            $fullPath = rtrim($basePath, '/') . '/' . $name;
            if ($basePath === '/') {
                $fullPath = '/' . $name;
            }

            $items[] = [
                'name' => $name,
                'type' => $isDirectory ? 'directory' : 'file',
                'path' => $fullPath
            ];
        }
    }

    // Ordenar: directorios primero, luego archivos, alfabéticamente
    usort($items, function($a, $b) {
        if ($a['type'] !== $b['type']) {
            return $a['type'] === 'directory' ? -1 : 1;
        }
        return strcasecmp($a['name'], $b['name']);
    });

    return $items;
}

function testPathAccess($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];
    
    $tempScript = tempnam(sys_get_temp_dir(), 'sftp_test');
    
    if (PHP_OS_FAMILY === 'Windows') {
        $sftpCommands = "cd \"$path\"\npwd\nquit\n";
        file_put_contents($tempScript, $sftpCommands);
        $command = "echo y | plink -sftp -P $port -l $username -pw \"$password\" $host -m \"$tempScript\" 2>&1";
    } else {
        $sftpCommands = "cd \"$path\"\npwd\nquit\n";
        file_put_contents($tempScript, $sftpCommands);
        $command = "sshpass -p \"$password\" sftp -P $port -o StrictHostKeyChecking=no -b \"$tempScript\" $username@$host 2>&1";
    }
    
    $output = [];
    exec($command, $output, $returnCode);
    unlink($tempScript);
    
    // Si el comando fue exitoso y no hay errores obvios
    return $returnCode === 0 && !empty($output) && !preg_match('/error|denied|failed/i', implode(' ', $output));
}
?>
