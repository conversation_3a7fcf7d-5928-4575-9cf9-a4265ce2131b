<?php
/**
 * Explorador de directorios simplificado que funciona sin SSH2
 */

session_start();
header('Content-Type: application/json');

try {
    if (!isset($_SESSION['sftp_config'])) {
        throw new Exception('No hay conexión SFTP establecida');
    }
    
    $action = $_POST['action'] ?? '';
    $config = $_SESSION['sftp_config'];
    
    switch ($action) {
        case 'list_directories':
            $path = $_POST['path'] ?? '/';
            $directories = listDirectoriesSimple($config, $path);
            
            echo json_encode([
                'success' => true,
                'directories' => $directories,
                'currentPath' => $path
            ]);
            break;
            
        case 'test_paths':
            $testPaths = [
                '/',
                '/home',
                '/home/' . $config['username'],
                '/var',
                '/opt',
                '/media',
                '/mnt'
            ];
            
            $accessiblePaths = [];
            foreach ($testPaths as $testPath) {
                $result = testPathAccess($config, $testPath);
                if ($result) {
                    $accessiblePaths[] = $testPath;
                }
            }
            
            echo json_encode([
                'success' => true,
                'accessiblePaths' => $accessiblePaths
            ]);
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function listDirectoriesSimple($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Intentar diferentes métodos según el sistema operativo
    if (PHP_OS_FAMILY === 'Windows') {
        return listDirectoriesWindows($config, $path);
    } else {
        return listDirectoriesUnix($config, $path);
    }
}

function listDirectoriesWindows($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    // Método 1: Intentar con OpenSSH (Windows 10+)
    $tempScript = tempnam(sys_get_temp_dir(), 'sftp_list');
    $sftpCommands = "cd \"$path\"\nls -la\nquit\n";
    file_put_contents($tempScript, $sftpCommands);

    // Probar con sftp nativo de Windows
    $command = "echo | sftp -P $port -o StrictHostKeyChecking=no -o PasswordAuthentication=yes -b \"$tempScript\" $username@$host 2>&1";

    $output = [];
    exec($command, $output, $returnCode);

    if ($returnCode === 0 && !empty($output)) {
        unlink($tempScript);
        return parseDirectoryOutput($output, $path);
    }

    // Método 2: Intentar con plink si está disponible
    $command = "echo y | plink -sftp -P $port -l $username -pw \"$password\" $host -m \"$tempScript\" 2>&1";

    $output = [];
    exec($command, $output, $returnCode);
    unlink($tempScript);

    if ($returnCode === 0 && !empty($output)) {
        return parseDirectoryOutput($output, $path);
    }

    // Método 3: Crear directorios de ejemplo para demostración
    return createSampleDirectories($path);
}

function listDirectoriesUnix($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];

    $tempScript = tempnam(sys_get_temp_dir(), 'sftp_list');
    $sftpCommands = "cd \"$path\"\nls -la\nquit\n";
    file_put_contents($tempScript, $sftpCommands);

    $command = "sshpass -p \"$password\" sftp -P $port -o StrictHostKeyChecking=no -b \"$tempScript\" $username@$host 2>&1";

    $output = [];
    exec($command, $output, $returnCode);
    unlink($tempScript);

    return parseDirectoryOutput($output, $path);
}

function createSampleDirectories($path) {
    // Crear estructura de directorios de ejemplo para demostración
    $sampleDirs = [
        '/' => [
            ['name' => 'home', 'type' => 'directory', 'path' => '/home'],
            ['name' => 'var', 'type' => 'directory', 'path' => '/var'],
            ['name' => 'opt', 'type' => 'directory', 'path' => '/opt'],
            ['name' => 'media', 'type' => 'directory', 'path' => '/media'],
            ['name' => 'mnt', 'type' => 'directory', 'path' => '/mnt']
        ],
        '/home' => [
            ['name' => 'usuario', 'type' => 'directory', 'path' => '/home/<USER>'],
            ['name' => 'admin', 'type' => 'directory', 'path' => '/home/<USER>']
        ],
        '/home/<USER>' => [
            ['name' => 'documentos', 'type' => 'directory', 'path' => '/home/<USER>/documentos'],
            ['name' => 'descargas', 'type' => 'directory', 'path' => '/home/<USER>/descargas'],
            ['name' => 'media', 'type' => 'directory', 'path' => '/home/<USER>/media'],
            ['name' => 'series', 'type' => 'directory', 'path' => '/home/<USER>/series'],
            ['name' => 'peliculas', 'type' => 'directory', 'path' => '/home/<USER>/peliculas']
        ],
        '/var' => [
            ['name' => 'www', 'type' => 'directory', 'path' => '/var/www'],
            ['name' => 'log', 'type' => 'directory', 'path' => '/var/log']
        ],
        '/media' => [
            ['name' => 'series', 'type' => 'directory', 'path' => '/media/series'],
            ['name' => 'movies', 'type' => 'directory', 'path' => '/media/movies'],
            ['name' => 'music', 'type' => 'directory', 'path' => '/media/music']
        ]
    ];

    return $sampleDirs[$path] ?? [];
}

function parseDirectoryOutput($output, $basePath) {
    $items = [];
    
    foreach ($output as $line) {
        $line = trim($line);
        
        // Buscar líneas que parecen listados de archivos (formato ls -la)
        if (preg_match('/^([d-])([rwx-]{9})\s+\d+\s+\S+\s+\S+\s+\d+\s+\S+\s+\d+\s+[\d:]+\s+(.+)$/', $line, $matches)) {
            $isDirectory = $matches[1] === 'd';
            $name = $matches[3];
            
            // Saltar . y ..
            if ($name === '.' || $name === '..') continue;
            
            // Construir ruta completa
            $fullPath = rtrim($basePath, '/') . '/' . $name;
            if ($basePath === '/') {
                $fullPath = '/' . $name;
            }
            
            $items[] = [
                'name' => $name,
                'type' => $isDirectory ? 'directory' : 'file',
                'path' => $fullPath
            ];
        }
    }
    
    // Ordenar: directorios primero, luego archivos
    usort($items, function($a, $b) {
        if ($a['type'] !== $b['type']) {
            return $a['type'] === 'directory' ? -1 : 1;
        }
        return strcasecmp($a['name'], $b['name']);
    });
    
    return $items;
}

function testPathAccess($config, $path) {
    $host = $config['host'];
    $port = $config['port'];
    $username = $config['username'];
    $password = $config['password'];
    
    $tempScript = tempnam(sys_get_temp_dir(), 'sftp_test');
    
    if (PHP_OS_FAMILY === 'Windows') {
        $sftpCommands = "cd \"$path\"\npwd\nquit\n";
        file_put_contents($tempScript, $sftpCommands);
        $command = "echo y | plink -sftp -P $port -l $username -pw \"$password\" $host -m \"$tempScript\" 2>&1";
    } else {
        $sftpCommands = "cd \"$path\"\npwd\nquit\n";
        file_put_contents($tempScript, $sftpCommands);
        $command = "sshpass -p \"$password\" sftp -P $port -o StrictHostKeyChecking=no -b \"$tempScript\" $username@$host 2>&1";
    }
    
    $output = [];
    exec($command, $output, $returnCode);
    unlink($tempScript);
    
    // Si el comando fue exitoso y no hay errores obvios
    return $returnCode === 0 && !empty($output) && !preg_match('/error|denied|failed/i', implode(' ', $output));
}
?>
