<?php
/**
 * Página de estado del sistema y configuración
 */

require_once 'm3u_series_downloader.php';

function checkRequirements() {
    $requirements = [
        'PHP Version' => [
            'check' => version_compare(PHP_VERSION, '7.0.0', '>='),
            'current' => PHP_VERSION,
            'required' => '7.0.0+',
            'critical' => true
        ],
        'SSH2 Extension' => [
            'check' => extension_loaded('ssh2'),
            'current' => extension_loaded('ssh2') ? 'Instalada' : 'No instalada',
            'required' => 'Requerida',
            'critical' => true
        ],
        'cURL Extension' => [
            'check' => extension_loaded('curl'),
            'current' => extension_loaded('curl') ? 'Instalada' : 'No instalada',
            'required' => 'Requerida',
            'critical' => true
        ],
        'JSON Extension' => [
            'check' => extension_loaded('json'),
            'current' => extension_loaded('json') ? 'Instalada' : 'No instalada',
            'required' => 'Requerida',
            'critical' => true
        ]
    ];
    
    return $requirements;
}

function checkDirectories() {
    $directories = [
        './downloads' => 'Directorio de descargas',
        './uploads' => 'Directorio de uploads',
    ];
    
    $results = [];
    foreach ($directories as $dir => $description) {
        $exists = is_dir($dir);
        $writable = $exists ? is_writable($dir) : false;
        
        if (!$exists) {
            mkdir($dir, 0755, true);
            $exists = is_dir($dir);
            $writable = $exists ? is_writable($dir) : false;
        }
        
        $results[$dir] = [
            'description' => $description,
            'exists' => $exists,
            'writable' => $writable
        ];
    }
    
    return $results;
}

function testSFTPConnection() {
    try {
        $downloader = new M3USeriesDownloader();
        $downloader->connectSFTP();
        $downloader->disconnect();
        return ['success' => true, 'message' => 'Conexión SFTP exitosa'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

$requirements = checkRequirements();
$directories = checkDirectories();
$sftpTest = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_sftp'])) {
    $sftpTest = testSFTPConnection();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estado del Sistema - Descargador M3U</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav {
            background: #34495e;
            padding: 15px 30px;
        }
        
        .nav a {
            color: white;
            text-decoration: none;
            margin-right: 20px;
            padding: 8px 15px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav a:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .content {
            padding: 30px;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .status-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .status-table th,
        .status-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .status-table th {
            background: #e9ecef;
            font-weight: 600;
        }
        
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ Estado del Sistema</h1>
            <p>Verificación de requisitos y configuración</p>
        </div>
        
        <div class="nav">
            <a href="index.php">🏠 Inicio</a>
            <a href="status.php">⚙️ Estado</a>
            <a href="get_log.php" target="_blank">📋 Ver Log</a>
        </div>
        
        <div class="content">
            <!-- Requisitos del Sistema -->
            <div class="card">
                <h3>🔧 Requisitos del Sistema</h3>
                <table class="status-table">
                    <thead>
                        <tr>
                            <th>Componente</th>
                            <th>Estado</th>
                            <th>Actual</th>
                            <th>Requerido</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($requirements as $name => $req): ?>
                            <tr>
                                <td><?php echo $name; ?></td>
                                <td class="<?php echo $req['check'] ? 'status-ok' : 'status-error'; ?>">
                                    <?php echo $req['check'] ? '✅ OK' : '❌ FALTA'; ?>
                                </td>
                                <td><?php echo $req['current']; ?></td>
                                <td><?php echo $req['required']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Directorios -->
            <div class="card">
                <h3>📁 Directorios del Sistema</h3>
                <table class="status-table">
                    <thead>
                        <tr>
                            <th>Directorio</th>
                            <th>Descripción</th>
                            <th>Existe</th>
                            <th>Escribible</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($directories as $dir => $info): ?>
                            <tr>
                                <td><code><?php echo $dir; ?></code></td>
                                <td><?php echo $info['description']; ?></td>
                                <td class="<?php echo $info['exists'] ? 'status-ok' : 'status-error'; ?>">
                                    <?php echo $info['exists'] ? '✅ Sí' : '❌ No'; ?>
                                </td>
                                <td class="<?php echo $info['writable'] ? 'status-ok' : 'status-error'; ?>">
                                    <?php echo $info['writable'] ? '✅ Sí' : '❌ No'; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Prueba de Conexión SFTP -->
            <div class="card">
                <h3>🌐 Conexión SFTP</h3>
                
                <?php if ($sftpTest): ?>
                    <div class="alert <?php echo $sftpTest['success'] ? 'alert-success' : 'alert-error'; ?>">
                        <?php if ($sftpTest['success']): ?>
                            ✅ Conexión SFTP exitosa
                        <?php else: ?>
                            ❌ Error de conexión SFTP: <?php echo htmlspecialchars($sftpTest['message']); ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
                <p>Prueba la conexión a tu servidor SFTP con la configuración actual:</p>
                <form method="post" style="margin-top: 15px;">
                    <button type="submit" name="test_sftp" class="btn">🔌 Probar Conexión SFTP</button>
                </form>
            </div>
            
            <!-- Información del Sistema -->
            <div class="card">
                <h3>💻 Información del Sistema</h3>
                <table class="status-table">
                    <tbody>
                        <tr>
                            <td><strong>Versión de PHP</strong></td>
                            <td><?php echo PHP_VERSION; ?></td>
                        </tr>
                        <tr>
                            <td><strong>Sistema Operativo</strong></td>
                            <td><?php echo PHP_OS; ?></td>
                        </tr>
                        <tr>
                            <td><strong>Memoria Límite</strong></td>
                            <td><?php echo ini_get('memory_limit'); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Tiempo Máximo de Ejecución</strong></td>
                            <td><?php echo ini_get('max_execution_time'); ?> segundos</td>
                        </tr>
                        <tr>
                            <td><strong>Tamaño Máximo de Upload</strong></td>
                            <td><?php echo ini_get('upload_max_filesize'); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Archivos de Configuración -->
            <div class="card">
                <h3>📄 Archivos del Sistema</h3>
                <table class="status-table">
                    <tbody>
                        <?php 
                        $files = [
                            'config.php' => 'Configuración principal',
                            'm3u_series_downloader.php' => 'Clase descargador',
                            'download_series.php' => 'Script CLI',
                            'index.php' => 'Interfaz web',
                            'download_background.php' => 'Descarga en segundo plano'
                        ];
                        
                        foreach ($files as $file => $description):
                            $exists = file_exists($file);
                        ?>
                            <tr>
                                <td><code><?php echo $file; ?></code></td>
                                <td><?php echo $description; ?></td>
                                <td class="<?php echo $exists ? 'status-ok' : 'status-error'; ?>">
                                    <?php echo $exists ? '✅ Existe' : '❌ Falta'; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
