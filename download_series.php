<?php
/**
 * Script principal para descargar series desde archivos M3U
 * 
 * Uso:
 * php download_series.php archivo.m3u [nombre_serie_opcional]
 */

require_once 'm3u_series_downloader.php';

function showUsage() {
    echo "Uso: php download_series.php <archivo_m3u> [nombre_serie]\n";
    echo "\nEjemplos:\n";
    echo "  php download_series.php mi_lista.m3u\n";
    echo "  php download_series.php mi_lista.m3u \"Run Coyote Run\"\n";
    echo "\nOpciones:\n";
    echo "  archivo_m3u    - Ruta al archivo M3U a procesar\n";
    echo "  nombre_serie   - (Opcional) Nombre específico de serie a descargar\n";
    exit(1);
}

function selectSeries($series) {
    echo "\n=== SELECCIONAR SERIE ===\n";
    $seriesNames = array_keys($series);
    
    for ($i = 0; $i < count($seriesNames); $i++) {
        $seriesName = $seriesNames[$i];
        $seasons = $series[$seriesName];
        $totalEpisodes = 0;
        foreach ($seasons as $episodes) {
            $totalEpisodes += count($episodes);
        }
        echo ($i + 1) . ". {$seriesName} (" . count($seasons) . " temporadas, {$totalEpisodes} episodios)\n";
    }
    
    echo "0. Descargar todas las series\n";
    echo "\nSelecciona una opción (0-" . count($seriesNames) . "): ";
    
    $handle = fopen("php://stdin", "r");
    $selection = trim(fgets($handle));
    fclose($handle);
    
    if ($selection === '0') {
        return null; // Descargar todas
    }
    
    $index = intval($selection) - 1;
    if ($index >= 0 && $index < count($seriesNames)) {
        return $seriesNames[$index];
    }
    
    echo "Selección inválida.\n";
    return selectSeries($series);
}

function confirmDownload($series, $selectedSeries = null) {
    echo "\n=== CONFIRMACIÓN ===\n";
    
    if ($selectedSeries) {
        $seasons = $series[$selectedSeries];
        $totalEpisodes = 0;
        foreach ($seasons as $episodes) {
            $totalEpisodes += count($episodes);
        }
        echo "Serie seleccionada: {$selectedSeries}\n";
        echo "Temporadas: " . count($seasons) . "\n";
        echo "Episodios totales: {$totalEpisodes}\n";
    } else {
        $totalSeries = count($series);
        $totalEpisodes = 0;
        foreach ($series as $seasons) {
            foreach ($seasons as $episodes) {
                $totalEpisodes += count($episodes);
            }
        }
        echo "Todas las series serán descargadas\n";
        echo "Total de series: {$totalSeries}\n";
        echo "Total de episodios: {$totalEpisodes}\n";
    }
    
    echo "\n¿Continuar con la descarga? (s/n): ";
    $handle = fopen("php://stdin", "r");
    $response = trim(fgets($handle));
    fclose($handle);
    
    return strtolower($response) === 's' || strtolower($response) === 'si' || strtolower($response) === 'y' || strtolower($response) === 'yes';
}

// Verificar argumentos
if ($argc < 2) {
    showUsage();
}

$m3uFile = $argv[1];
$specificSeries = isset($argv[2]) ? $argv[2] : null;

// Verificar que el archivo M3U existe
if (!file_exists($m3uFile)) {
    echo "Error: Archivo M3U no encontrado: {$m3uFile}\n";
    exit(1);
}

try {
    echo "=== DESCARGADOR DE SERIES M3U ===\n";
    echo "Archivo M3U: {$m3uFile}\n";
    
    // Crear instancia del descargador
    $downloader = new M3USeriesDownloader();
    
    // Parsear archivo M3U
    echo "\nPaso 1: Parseando archivo M3U...\n";
    $entries = $downloader->parseM3U($m3uFile);
    
    if (empty($entries)) {
        echo "No se encontraron entradas válidas en el archivo M3U.\n";
        exit(1);
    }
    
    // Identificar series
    echo "\nPaso 2: Identificando series...\n";
    $series = $downloader->identifySeries($entries);
    
    if (empty($series)) {
        echo "No se encontraron series con formato de temporada/episodio válido.\n";
        echo "Asegúrate de que los títulos contengan patrones como S01E01 o 1x01.\n";
        exit(1);
    }
    
    // Mostrar series encontradas
    $downloader->listSeries($series);
    
    // Seleccionar serie si no se especificó una
    $selectedSeries = $specificSeries;
    if (!$selectedSeries) {
        $selectedSeries = selectSeries($series);
    } else {
        // Verificar que la serie especificada existe
        if (!isset($series[$selectedSeries])) {
            echo "Error: Serie '{$selectedSeries}' no encontrada en el archivo M3U.\n";
            echo "Series disponibles:\n";
            foreach (array_keys($series) as $name) {
                echo "  - {$name}\n";
            }
            exit(1);
        }
    }
    
    // Confirmar descarga
    if (!confirmDownload($series, $selectedSeries)) {
        echo "Descarga cancelada.\n";
        exit(0);
    }
    
    // Conectar al servidor SFTP
    echo "\nPaso 3: Conectando al servidor SFTP...\n";
    $downloader->connectSFTP();
    
    // Iniciar descarga
    echo "\nPaso 4: Iniciando descarga...\n";
    $downloader->downloadSeries($series, $selectedSeries);
    
    echo "\n=== DESCARGA COMPLETADA ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
} finally {
    // Desconectar
    if (isset($downloader)) {
        $downloader->disconnect();
    }
}
