<?php
/**
 * Manejador de descargas final - Funciona en cualquier entorno
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!headers_sent()) {
    header('Content-Type: application/json');
}

class FinalDownloadHandler {
    private $progressFile = './download_progress.json';
    private $pidFile = './download.pid';
    private $logFile = './download_output.log';
    
    public function executeDownload() {
        try {
            // Verificar configuración
            if (!isset($_SESSION['sftp_config']) || !isset($_SESSION['series']) || !isset($_SESSION['download_config'])) {
                throw new Exception('Configuración incompleta. Asegúrate de configurar SFTP, cargar series y configurar descarga.');
            }
            
            $sftpConfig = $_SESSION['sftp_config'];
            $series = $_SESSION['series'];
            $downloadConfig = $_SESSION['download_config'];
            
            // Preparar archivos
            $files = $this->prepareFileList($series, $downloadConfig);
            
            if (empty($files)) {
                throw new Exception('No hay archivos para descargar.');
            }
            
            // Detectar método disponible
            $method = $this->detectBestMethod();
            
            // Iniciar descarga
            $pid = $this->startDownload($files, $method);
            
            // Guardar estado
            file_put_contents($this->pidFile, $pid);
            $this->saveProgress([
                'percent' => 0,
                'currentFile' => 'Iniciando descarga...',
                'speed' => '0 KB/s',
                'timeRemaining' => '--:--',
                'files' => $files,
                'completed' => false,
                'error' => null,
                'total_files' => count($files),
                'method' => $method
            ]);
            
            return [
                'success' => true,
                'pid' => $pid,
                'message' => "Descarga iniciada usando: $method",
                'method' => $method,
                'total_files' => count($files)
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    private function detectBestMethod() {
        // Verificar en orden de preferencia
        if (function_exists('file_get_contents')) {
            return 'PHP file_get_contents';
        } elseif (function_exists('curl_init')) {
            return 'PHP cURL';
        } else {
            throw new Exception('No hay métodos de descarga disponibles');
        }
    }
    
    private function startDownload($files, $method) {
        // Crear script de descarga simple
        $scriptContent = $this->generateDownloadScript($files, $method);
        $scriptPath = './simple_downloader.php';
        file_put_contents($scriptPath, $scriptContent);
        
        // Ejecutar en segundo plano
        if (function_exists('proc_open')) {
            return $this->executeWithProcOpen($scriptPath);
        } else {
            // Ejecutar inmediatamente (sin segundo plano)
            return $this->executeImmediately($scriptPath);
        }
    }
    
    private function executeWithProcOpen($scriptPath) {
        $descriptorspec = [
            0 => ["pipe", "r"],
            1 => ["pipe", "w"],
            2 => ["pipe", "w"]
        ];
        
        $command = "php " . escapeshellarg($scriptPath);
        $process = proc_open($command, $descriptorspec, $pipes, getcwd());
        
        if (is_resource($process)) {
            fclose($pipes[0]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            
            $status = proc_get_status($process);
            $pid = $status['pid'] ?? getmypid();
            proc_close($process);
            
            return $pid;
        } else {
            throw new Exception('No se pudo iniciar el proceso de descarga');
        }
    }
    
    private function executeImmediately($scriptPath) {
        // Ejecutar el script inmediatamente (modo síncrono)
        include $scriptPath;
        return getmypid();
    }
    
    private function generateDownloadScript($files, $method) {
        $script = "<?php\n";
        $script .= "// Script de descarga generado automáticamente\n";
        $script .= "set_time_limit(0);\n";
        $script .= "ini_set('memory_limit', '256M');\n\n";
        
        $script .= "\$progressFile = " . var_export($this->progressFile, true) . ";\n";
        $script .= "\$logFile = " . var_export($this->logFile, true) . ";\n";
        $script .= "\$files = " . var_export($files, true) . ";\n";
        $script .= "\$totalFiles = " . count($files) . ";\n\n";
        
        $script .= "function logMessage(\$message) {\n";
        $script .= "    global \$logFile;\n";
        $script .= "    \$timestamp = date('Y-m-d H:i:s');\n";
        $script .= "    file_put_contents(\$logFile, \"[\$timestamp] \$message\\n\", FILE_APPEND | LOCK_EX);\n";
        $script .= "}\n\n";
        
        $script .= "function updateProgress(\$percent, \$currentFile, \$completed = false) {\n";
        $script .= "    global \$progressFile, \$totalFiles;\n";
        $script .= "    \$data = [\n";
        $script .= "        'percent' => \$percent,\n";
        $script .= "        'currentFile' => \$currentFile,\n";
        $script .= "        'speed' => '0 KB/s',\n";
        $script .= "        'timeRemaining' => '--:--',\n";
        $script .= "        'completed' => \$completed,\n";
        $script .= "        'total_files' => \$totalFiles\n";
        $script .= "    ];\n";
        $script .= "    file_put_contents(\$progressFile, json_encode(\$data), LOCK_EX);\n";
        $script .= "}\n\n";
        
        $script .= "function downloadFile(\$url, \$outputPath) {\n";
        $script .= "    \$context = stream_context_create([\n";
        $script .= "        'http' => [\n";
        $script .= "            'timeout' => 120,\n";
        $script .= "            'user_agent' => 'M3U-Downloader/1.0',\n";
        $script .= "            'follow_location' => true,\n";
        $script .= "            'max_redirects' => 3\n";
        $script .= "        ]\n";
        $script .= "    ]);\n";
        $script .= "    \n";
        $script .= "    \$data = @file_get_contents(\$url, false, \$context);\n";
        $script .= "    \n";
        $script .= "    if (\$data !== false) {\n";
        $script .= "        \$dir = dirname(\$outputPath);\n";
        $script .= "        if (!is_dir(\$dir)) {\n";
        $script .= "            @mkdir(\$dir, 0755, true);\n";
        $script .= "        }\n";
        $script .= "        return file_put_contents(\$outputPath, \$data, LOCK_EX) !== false;\n";
        $script .= "    }\n";
        $script .= "    \n";
        $script .= "    return false;\n";
        $script .= "}\n\n";
        
        $script .= "// Iniciar descarga\n";
        $script .= "logMessage('Iniciando descarga de ' . \$totalFiles . ' archivos');\n";
        $script .= "updateProgress(0, 'Iniciando descarga...');\n\n";
        
        $script .= "foreach (\$files as \$index => \$file) {\n";
        $script .= "    \$percent = round(((\$index + 1) / \$totalFiles) * 100, 1);\n";
        $script .= "    \$filename = \$file['filename'];\n";
        $script .= "    \$localFile = './downloads/' . \$filename;\n";
        $script .= "    \n";
        $script .= "    updateProgress(\$percent, 'Descargando: ' . \$filename);\n";
        $script .= "    logMessage('Descargando: ' . \$filename);\n";
        $script .= "    \n";
        $script .= "    if (downloadFile(\$file['url'], \$localFile)) {\n";
        $script .= "        \$size = filesize(\$localFile);\n";
        $script .= "        logMessage('✅ Descargado: ' . \$filename . ' (' . \$size . ' bytes)');\n";
        $script .= "    } else {\n";
        $script .= "        logMessage('❌ Error descargando: ' . \$filename);\n";
        $script .= "    }\n";
        $script .= "    \n";
        $script .= "    // Pausa pequeña para no sobrecargar\n";
        $script .= "    usleep(500000); // 0.5 segundos\n";
        $script .= "}\n\n";
        
        $script .= "// Finalizar\n";
        $script .= "updateProgress(100, 'Descarga completada', true);\n";
        $script .= "logMessage('🎉 Descarga completada exitosamente');\n\n";
        
        $script .= "// Limpiar\n";
        $script .= "@unlink(__FILE__);\n";
        $script .= "?>";
        
        return $script;
    }
    
    private function prepareFileList($series, $downloadConfig) {
        $files = [];
        $selectedSeries = $downloadConfig['selected_series'];
        $downloadPath = $downloadConfig['download_path'];
        
        foreach ($series as $seriesName => $seasons) {
            if ($selectedSeries && $selectedSeries !== $seriesName) {
                continue;
            }
            
            foreach ($seasons as $seasonNum => $episodes) {
                foreach ($episodes as $episodeNum => $entry) {
                    $url = $entry['url'];
                    $title = $entry['title'];
                    
                    $extension = $this->getFileExtension($url);
                    $filename = sprintf("S%02dE%02d - %s.%s", 
                        $seasonNum, 
                        $episodeNum, 
                        $this->cleanFileName($title), 
                        $extension
                    );
                    
                    $files[] = [
                        'url' => $url,
                        'filename' => $filename,
                        'series' => $seriesName,
                        'season' => $seasonNum,
                        'episode' => $episodeNum
                    ];
                }
            }
        }
        
        return $files;
    }
    
    private function getFileExtension($url) {
        $path = parse_url($url, PHP_URL_PATH);
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        return $extension ?: 'mp4';
    }
    
    private function cleanFileName($name) {
        if ($name === null || $name === '') {
            return 'archivo_sin_nombre';
        }
        
        $name = preg_replace('/[<>:"\\/\\\\|?*]/', '', $name);
        $name = trim($name, '. ');
        return substr($name, 0, 80);
    }
    
    private function saveProgress($data) {
        file_put_contents($this->progressFile, json_encode($data, JSON_PRETTY_PRINT), LOCK_EX);
    }
}

// Procesar solicitud
$handler = new FinalDownloadHandler();
$action = $_POST['action'] ?? '';

switch ($action) {
    case 'execute_download':
        echo json_encode($handler->executeDownload());
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Acción no válida']);
}
?>
