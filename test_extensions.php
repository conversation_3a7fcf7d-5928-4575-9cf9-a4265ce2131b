<?php
/**
 * Script simple para verificar extensiones PHP
 */

echo "=== VERIFICACIÓN DE EXTENSIONES PHP ===\n\n";

echo "Versión de PHP: " . PHP_VERSION . "\n\n";

$extensions = ['ssh2', 'curl', 'json', 'openssl'];

foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo sprintf("%-10s: %s\n", $ext, $loaded ? '✅ Instalada' : '❌ No instalada');
}

echo "\n=== FUNCIONES DISPONIBLES ===\n\n";

$functions = [
    'ssh2_connect',
    'ssh2_auth_password', 
    'ssh2_sftp',
    'curl_init',
    'json_encode'
];

foreach ($functions as $func) {
    $exists = function_exists($func);
    echo sprintf("%-20s: %s\n", $func, $exists ? '✅ Disponible' : '❌ No disponible');
}

echo "\n=== INFORMACIÓN DEL SISTEMA ===\n\n";
echo "Sistema operativo: " . PHP_OS . "\n";
echo "Arquitectura: " . php_uname('m') . "\n";
echo "Servidor web: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'CLI') . "\n";

if (extension_loaded('ssh2')) {
    echo "\n=== PRUEBA BÁSICA SSH2 ===\n\n";
    echo "Intentando crear una conexión SSH2 de prueba...\n";
    
    // Intentar conectar a localhost (esto fallará pero nos dirá si la extensión funciona)
    $connection = @ssh2_connect('127.0.0.1', 22);
    if ($connection === false) {
        echo "✅ Extensión SSH2 funciona correctamente (conexión falló como se esperaba)\n";
    } else {
        echo "⚠️ Conexión SSH2 inesperadamente exitosa\n";
        ssh2_disconnect($connection);
    }
} else {
    echo "\n❌ No se puede probar SSH2 - extensión no instalada\n";
}

echo "\n=== COMANDOS DE INSTALACIÓN ===\n\n";

if (PHP_OS_FAMILY === 'Linux') {
    echo "Para Ubuntu/Debian:\n";
    echo "sudo apt-get install php-ssh2 php-curl\n\n";
    
    echo "Para CentOS/RHEL:\n";
    echo "sudo yum install php-ssh2 php-curl\n\n";
    
    echo "Usando PECL:\n";
    echo "sudo pecl install ssh2\n";
    echo "Luego agregar 'extension=ssh2.so' a php.ini\n\n";
} elseif (PHP_OS_FAMILY === 'Darwin') {
    echo "Para macOS con Homebrew:\n";
    echo "brew install php\n";
    echo "pecl install ssh2\n\n";
} elseif (PHP_OS_FAMILY === 'Windows') {
    echo "Para Windows:\n";
    echo "1. Descargar php_ssh2.dll desde https://pecl.php.net/package/ssh2\n";
    echo "2. Colocar en el directorio ext/ de PHP\n";
    echo "3. Agregar 'extension=ssh2' a php.ini\n";
    echo "4. Reiniciar el servidor web\n\n";
}

echo "Después de instalar, reinicia el servidor web y ejecuta este script nuevamente.\n";
?>
