<?php
/**
 * Script para probar conexión desde Windows a servidor Linux
 */

echo "=== PRUEBA: WINDOWS → SERVIDOR LINUX ===\n\n";

echo "Sistema local: " . PHP_OS_FAMILY . " (" . php_uname('s') . ")\n";
echo "Objetivo: Conectar a servidor Linux remoto via SFTP\n\n";

// Verificar herramientas disponibles en Windows
echo "1. Verificando herramientas SSH en Windows...\n";

$tools = [
    'ssh' => 'OpenSSH Client (Windows 10+)',
    'sftp' => 'SFTP Client (OpenSSH)',
    'plink' => 'PuTTY Link (PuTTY)',
    'pscp' => 'PuTTY SCP (PuTTY)'
];

$availableTools = [];

foreach ($tools as $command => $description) {
    $output = [];
    $returnCode = 0;
    exec("where $command 2>nul", $output, $returnCode);
    
    if ($returnCode === 0 && !empty($output)) {
        echo "  ✅ $command: $description\n";
        echo "     Ubicación: " . $output[0] . "\n";
        $availableTools[] = $command;
    } else {
        echo "  ❌ $command: No encontrado\n";
    }
}

echo "\n";

if (empty($availableTools)) {
    echo "❌ No se encontraron herramientas SSH.\n\n";
    echo "SOLUCIONES:\n";
    echo "1. Instalar OpenSSH (recomendado):\n";
    echo "   winget install Microsoft.OpenSSH.Beta\n\n";
    echo "2. Instalar PuTTY:\n";
    echo "   winget install PuTTY.PuTTY\n\n";
    echo "3. Habilitar OpenSSH en Windows:\n";
    echo "   - Configuración > Aplicaciones > Características opcionales\n";
    echo "   - Agregar 'Cliente OpenSSH'\n\n";
    exit(1);
}

echo "✅ Herramientas SSH disponibles: " . implode(', ', $availableTools) . "\n\n";

// Configuración de prueba para servidor Linux
echo "2. Configuración de prueba...\n";

$testConfigs = [
    [
        'name' => 'Servidor público de prueba',
        'host' => 'test.rebex.net',
        'port' => 22,
        'username' => 'demo',
        'password' => 'password',
        'description' => 'Servidor SFTP público para pruebas'
    ]
];

echo "Servidores de prueba disponibles:\n";
foreach ($testConfigs as $i => $config) {
    echo "  " . ($i + 1) . ". {$config['name']}\n";
    echo "     Host: {$config['host']}\n";
    echo "     Usuario: {$config['username']}\n";
    echo "     Descripción: {$config['description']}\n\n";
}

// Probar conexión básica
echo "3. Probando conexión SSH básica...\n";

$config = $testConfigs[0]; // Usar el primer servidor de prueba

if (in_array('ssh', $availableTools)) {
    echo "Probando con OpenSSH...\n";
    
    $command = "ssh -p {$config['port']} -o ConnectTimeout=10 -o StrictHostKeyChecking=no {$config['username']}@{$config['host']} \"pwd && ls -la\" 2>&1";
    
    echo "Comando: $command\n";
    echo "Nota: Se te pedirá la contraseña: {$config['password']}\n\n";
    
    $output = [];
    exec($command, $output, $returnCode);
    
    echo "Resultado:\n";
    foreach ($output as $line) {
        echo "  $line\n";
    }
    echo "Código de salida: $returnCode\n\n";
    
    if ($returnCode === 0) {
        echo "✅ Conexión SSH exitosa!\n";
    } else {
        echo "❌ Conexión SSH falló\n";
    }
}

// Probar SFTP específicamente
echo "4. Probando SFTP para navegación de directorios...\n";

if (in_array('sftp', $availableTools)) {
    echo "Probando listado de directorios con SFTP...\n";
    
    // Crear script SFTP temporal
    $tempScript = tempnam(sys_get_temp_dir(), 'sftp_test');
    $sftpCommands = "pwd\nls -la\ncd /\nls -la\nquit\n";
    file_put_contents($tempScript, $sftpCommands);
    
    $command = "sftp -P {$config['port']} -o StrictHostKeyChecking=no -b \"$tempScript\" {$config['username']}@{$config['host']} 2>&1";
    
    echo "Comando SFTP: $command\n";
    echo "Script temporal: $tempScript\n";
    echo "Contenido del script:\n$sftpCommands\n";
    
    $output = [];
    exec($command, $output, $returnCode);
    
    echo "Resultado SFTP:\n";
    foreach ($output as $line) {
        echo "  $line\n";
    }
    
    unlink($tempScript);
    
    if ($returnCode === 0) {
        echo "✅ SFTP funcionando correctamente!\n";
    } else {
        echo "❌ SFTP falló\n";
    }
}

echo "\n=== RESUMEN ===\n\n";

echo "Para usar el explorador de directorios:\n";
echo "1. Asegúrate de tener OpenSSH o PuTTY instalado\n";
echo "2. Configura tu servidor Linux real en la interfaz web\n";
echo "3. El explorador navegará por las carpetas del servidor Linux\n";
echo "4. Podrás seleccionar rutas como /home/<USER>/series\n\n";

echo "Estructura típica de servidor Linux:\n";
echo "  /home/<USER>/          (directorio del usuario)\n";
echo "  /home/<USER>/series/   (para guardar series)\n";
echo "  /var/www/               (archivos web)\n";
echo "  /media/                 (medios montados)\n";
echo "  /opt/                   (aplicaciones opcionales)\n\n";

echo "¡Prueba completada!\n";
?>
