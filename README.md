# Descargador de Series M3U con SFTP

Este sistema PHP permite descargar series de televisión organizadas desde archivos M3U, conectándose a servidores remotos via SFTP o HTTP/HTTPS y organizando automáticamente los archivos en carpetas por serie, temporada y episodio.

## 🌟 Características

- ✅ **Interfaz Web Moderna**: Interfaz gráfica intuitiva y responsive
- ✅ **Parseo de archivos M3U**: Lee y analiza archivos M3U estándar
- ✅ **Detección automática de series**: Identifica patrones como S01E01, S02E01, 1x01, etc.
- ✅ **Conexión SFTP**: Se conecta a servidores remotos via SFTP
- ✅ **Descarga HTTP/HTTPS**: Soporte para URLs web estándar
- ✅ **Organización automática**: Crea estructura de carpetas por serie/temporada
- ✅ **Selección interactiva**: Permite elegir qué serie descargar
- ✅ **Monitor en tiempo real**: Seguimiento del progreso de descarga
- ✅ **Logging completo**: Registro detallado de todas las operaciones
- ✅ **Manejo de errores**: Reintentos automáticos y manejo robusto de errores
- ✅ **Verificación de sistema**: Comprobación automática de requisitos

## Requisitos

### PHP y Extensiones
```bash
# Ubuntu/Debian
sudo apt-get install php php-ssh2 php-curl

# CentOS/RHEL
sudo yum install php php-ssh2 php-curl

# Windows (usando XAMPP o similar)
# Habilitar extensiones en php.ini:
# extension=ssh2
# extension=curl
```

### Verificar extensiones
```bash
php -m | grep -E "(ssh2|curl)"
```

## Instalación

1. **Clonar o descargar los archivos**:
   ```bash
   git clone <este-repositorio>
   cd m3u-series-downloader
   ```

2. **Configurar el archivo config.php**:
   ```php
   <?php
   return [
       'sftp' => [
           'host' => 'tu-servidor.com',
           'port' => 22,
           'username' => 'tu_usuario',
           'password' => 'tu_password',
       ],
       // ... resto de configuración
   ];
   ```

3. **Configurar servidor web** (para interfaz web):
   ```bash
   # Asegurar que el servidor web puede escribir en los directorios
   mkdir downloads uploads
   chmod 755 downloads uploads

   # Para Apache, asegurar que mod_rewrite esté habilitado
   sudo a2enmod rewrite
   sudo systemctl restart apache2
   ```

4. **Verificar instalación**:
   ```
   # Via web
   http://tu-servidor/status.php

   # Via línea de comandos
   php check_requirements.php
   ```

## 🚀 Uso

### Interfaz Web (Recomendado)

1. **Acceder a la interfaz web**:
   ```
   http://tu-servidor/index.php
   ```

2. **Subir archivo M3U**:
   - Haz clic en "Selecciona tu archivo M3U"
   - Elige tu archivo .m3u o .m3u8
   - Haz clic en "📤 Subir y Analizar"

3. **Seleccionar series**:
   - Revisa las estadísticas mostradas
   - Selecciona "Descargar todas las series" o elige una específica
   - Haz clic en "🚀 Iniciar Descarga"

4. **Monitorear progreso**:
   - Ve a la sección "📊 Monitor de Progreso"
   - Haz clic en "🔄 Actualizar Log" para ver el estado

### Línea de Comandos
```bash
# Descargar todas las series del archivo M3U
php download_series.php mi_archivo.m3u

# Descargar una serie específica
php download_series.php mi_archivo.m3u "Run Coyote Run"
```

### Ejemplo de Flujo de Trabajo

1. **Preparar archivo M3U**:
   ```m3u
   #EXTM3U
   #EXTINF:-1,Run Coyote Run S01E01 - Pilot
   http://servidor.com/series/run_coyote_run/s01e01.mp4
   #EXTINF:-1,Run Coyote Run S01E02 - The Chase
   http://servidor.com/series/run_coyote_run/s01e02.mp4
   ```

2. **Ejecutar el script**:
   ```bash
   php download_series.php mi_lista.m3u
   ```

3. **Seleccionar serie** (si hay múltiples):
   ```
   === SERIES DISPONIBLES ===
   1. Run Coyote Run (2 temporadas, 24 episodios)
   2. Breaking Bad (5 temporadas, 62 episodios)
   0. Descargar todas las series
   
   Selecciona una opción (0-2): 1
   ```

4. **Confirmar descarga**:
   ```
   === CONFIRMACIÓN ===
   Serie seleccionada: Run Coyote Run
   Temporadas: 2
   Episodios totales: 24
   
   ¿Continuar con la descarga? (s/n): s
   ```

## Estructura de Carpetas Resultante

```
downloads/
├── Run Coyote Run/
│   ├── Temporada 1/
│   │   ├── S01E01 - Pilot.mp4
│   │   ├── S01E02 - The Chase.mp4
│   │   └── ...
│   └── Temporada 2/
│       ├── S02E01 - New Territory.mp4
│       └── ...
└── Breaking Bad/
    ├── Temporada 1/
    │   ├── S01E01 - Pilot.mkv
    │   └── ...
    └── Temporada 2/
        └── ...
```

## Configuración Avanzada

### Autenticación con Clave Privada
```php
'sftp' => [
    'host' => 'tu-servidor.com',
    'username' => 'tu_usuario',
    'private_key' => '/path/to/private/key',
    'public_key' => '/path/to/public/key',
    'passphrase' => 'passphrase_if_needed',
],
```

### Patrones Personalizados
```php
'patterns' => [
    'season_episode' => '/[Ss](\d+)[Ee](\d+)/',
    'season_episode_alt' => '/(\d+)x(\d+)/',
    'custom_pattern' => '/Temporada[\s]*(\d+)[\s]*Episodio[\s]*(\d+)/',
],
```

### Configuración de Descarga
```php
'download' => [
    'base_directory' => './mis_series/',
    'timeout' => 600,           // 10 minutos
    'max_retries' => 5,
    'chunk_size' => 16384,      // 16KB chunks
],
```

## Formatos de M3U Soportados

### Formato Estándar
```m3u
#EXTM3U
#EXTINF:duration,Title
URL
```

### Ejemplos de Títulos Reconocidos
- `Serie Name S01E01 - Episode Title`
- `Serie Name S1E1 - Episode Title`
- `Serie Name 1x01 - Episode Title`
- `Serie Name Season 1 Episode 1`

## Solución de Problemas

### Error: "No module named ssh2"
```bash
# Ubuntu/Debian
sudo apt-get install libssh2-1-dev
sudo pecl install ssh2

# Agregar a php.ini
echo "extension=ssh2.so" >> /etc/php/7.4/cli/php.ini
```

### Error: "Permission denied"
- Verificar credenciales SFTP en `config.php`
- Comprobar permisos del directorio de descarga
- Verificar conectividad de red al servidor

### Error: "No series found"
- Verificar que los títulos en el M3U contengan patrones como S01E01
- Revisar los patrones en la configuración
- Usar el archivo `example.m3u` para probar

### Archivos no se descargan
- Verificar URLs en el archivo M3U
- Comprobar conectividad a los servidores
- Revisar logs en `download.log`

## Logging

Los logs se guardan en `download.log` por defecto:
```bash
tail -f download.log
```

Niveles de log disponibles: DEBUG, INFO, WARNING, ERROR

## Contribuir

1. Fork el proyecto
2. Crear rama para feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## Licencia

Este proyecto está bajo la Licencia MIT. Ver archivo `LICENSE` para más detalles.

## Soporte

Para reportar bugs o solicitar features, crear un issue en el repositorio del proyecto.
