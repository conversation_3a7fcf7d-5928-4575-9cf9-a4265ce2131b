<?php
/**
 * Verificación del entorno Hostinger para conexiones SFTP remotas
 */

echo "=== VERIFICACIÓN DEL ENTORNO HOSTINGER ===\n\n";

// Información del sistema
echo "🖥️ Información del servidor:\n";
echo "Sistema operativo: " . PHP_OS . "\n";
echo "Nombre del servidor: " . php_uname('n') . "\n";
echo "Versión del kernel: " . php_uname('r') . "\n";
echo "Arquitectura: " . php_uname('m') . "\n";
echo "Versión de PHP: " . PHP_VERSION . "\n\n";

// Verificar herramientas SSH necesarias
echo "🔧 Verificando herramientas SSH/SFTP:\n";

$tools = [
    'ssh' => 'Cliente SSH',
    'scp' => 'Secure Copy',
    'sftp' => 'Cliente SFTP',
    'sshpass' => 'SSH con contraseña automática',
    'wget' => 'Descargador web',
    'curl' => 'Cliente HTTP/HTTPS',
    'expect' => 'Automatización de comandos interactivos'
];

$availableTools = [];

foreach ($tools as $command => $description) {
    $output = [];
    $returnCode = 0;
    exec("which $command 2>/dev/null", $output, $returnCode);
    
    if ($returnCode === 0 && !empty($output)) {
        echo "  ✅ $command: $description\n";
        echo "     Ubicación: " . $output[0] . "\n";
        $availableTools[] = $command;
    } else {
        echo "  ❌ $command: No disponible\n";
    }
}

echo "\n";

// Verificar extensiones PHP
echo "🐘 Verificando extensiones PHP:\n";

$extensions = ['curl', 'json', 'openssl', 'ssh2'];

foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "  " . ($loaded ? '✅' : '❌') . " $ext: " . ($loaded ? 'Instalada' : 'No instalada') . "\n";
}

echo "\n";

// Verificar permisos de escritura
echo "📁 Verificando permisos de escritura:\n";

$testDirs = ['./downloads', './uploads', './temp', sys_get_temp_dir()];

foreach ($testDirs as $dir) {
    if (!is_dir($dir) && $dir !== sys_get_temp_dir()) {
        @mkdir($dir, 0755, true);
    }
    
    if (is_writable($dir)) {
        echo "  ✅ $dir: Escribible\n";
    } else {
        echo "  ❌ $dir: No escribible\n";
    }
}

echo "\n";

// Probar descarga con wget
echo "🌐 Probando descarga con wget:\n";

if (in_array('wget', $availableTools)) {
    $testUrl = 'https://httpbin.org/json';
    $tempFile = tempnam(sys_get_temp_dir(), 'wget_test');
    
    $command = "wget --timeout=10 --tries=1 -q -O " . escapeshellarg($tempFile) . " " . escapeshellarg($testUrl) . " 2>&1";
    
    $output = [];
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0 && file_exists($tempFile) && filesize($tempFile) > 0) {
        echo "  ✅ wget funcionando correctamente\n";
        echo "     Archivo descargado: " . filesize($tempFile) . " bytes\n";
        unlink($tempFile);
    } else {
        echo "  ❌ wget falló\n";
        if (!empty($output)) {
            echo "     Error: " . implode(' ', $output) . "\n";
        }
    }
} else {
    echo "  ⏭️ wget no disponible, saltando prueba\n";
}

echo "\n";

// Probar conexión SSH a servidor público
echo "🔐 Probando conexión SSH a servidor público:\n";

if (in_array('sshpass', $availableTools) && in_array('ssh', $availableTools)) {
    $testHost = 'test.rebex.net';
    $testUser = 'demo';
    $testPass = 'password';
    
    echo "  Probando conexión a $testHost...\n";
    
    $command = "sshpass -p '$testPass' ssh -p 22 -o ConnectTimeout=10 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $testUser@$testHost 'pwd && ls -la' 2>&1";
    
    $output = [];
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "  ✅ Conexión SSH exitosa\n";
        echo "     Directorio remoto: " . (isset($output[0]) ? $output[0] : 'N/A') . "\n";
    } else {
        echo "  ⚠️ Conexión SSH falló (normal si el servidor está ocupado)\n";
        if (!empty($output)) {
            echo "     Salida: " . implode(' ', array_slice($output, 0, 2)) . "\n";
        }
    }
} else {
    echo "  ⏭️ sshpass o ssh no disponibles, saltando prueba\n";
}

echo "\n";

// Resumen y recomendaciones
echo "=== RESUMEN Y RECOMENDACIONES ===\n\n";

$criticalTools = ['ssh', 'scp', 'sftp', 'wget'];
$missingCritical = array_diff($criticalTools, $availableTools);

if (empty($missingCritical)) {
    echo "✅ ¡Excelente! Todas las herramientas críticas están disponibles.\n";
    echo "Tu servidor Hostinger está listo para:\n";
    echo "  - Conectarse a servidores remotos via SFTP\n";
    echo "  - Descargar archivos con wget\n";
    echo "  - Subir archivos a servidores remotos\n";
    echo "  - Navegar directorios remotos\n\n";
} else {
    echo "⚠️ Faltan herramientas críticas: " . implode(', ', $missingCritical) . "\n";
    echo "Contacta al soporte de Hostinger para instalar:\n";
    foreach ($missingCritical as $tool) {
        echo "  - $tool\n";
    }
    echo "\n";
}

if (!in_array('sshpass', $availableTools)) {
    echo "💡 Recomendación: sshpass no está disponible.\n";
    echo "El sistema usará métodos alternativos para autenticación.\n";
    echo "Si tienes problemas, contacta soporte de Hostinger.\n\n";
}

echo "🎯 Para usar el sistema:\n";
echo "1. Sube todos los archivos PHP a tu hosting Hostinger\n";
echo "2. Accede a tu dominio/subdirectorio donde subiste los archivos\n";
echo "3. Configura la conexión SFTP a tu servidor remoto\n";
echo "4. El sistema descargará desde Hostinger y subirá al servidor remoto\n\n";

echo "📋 Flujo de trabajo:\n";
echo "Internet → Hostinger (descarga) → Servidor remoto (almacenamiento)\n\n";

echo "🔧 Estructura recomendada en servidor remoto:\n";
echo "/home/<USER>/media/series/\n";
echo "/var/www/html/media/\n";
echo "/media/series/\n\n";

echo "¡Verificación completada!\n";
?>
