<?php
/**
 * Script para probar wget y comandos del sistema
 */

echo "=== PRUEBA DE WGET Y COMANDOS DEL SISTEMA ===\n\n";

// Verificar wget
echo "1. Verificando wget...\n";
$output = [];
$returnCode = 0;

if (PHP_OS_FAMILY === 'Windows') {
    exec('wget --version 2>&1', $output, $returnCode);
} else {
    exec('which wget 2>/dev/null', $output, $returnCode);
}

if ($returnCode === 0 && !empty($output)) {
    echo "✅ wget está disponible\n";
    if (PHP_OS_FAMILY === 'Windows') {
        echo "Versión: " . $output[0] . "\n";
    } else {
        echo "Ubicación: " . $output[0] . "\n";
    }
} else {
    echo "❌ wget no está disponible\n";
    echo "Instala wget:\n";
    if (PHP_OS_FAMILY === 'Windows') {
        echo "- Descarga desde: https://eternallybored.org/misc/wget/\n";
        echo "- O instala con chocolatey: choco install wget\n";
    } else {
        echo "- Ubuntu/Debian: sudo apt-get install wget\n";
        echo "- CentOS/RHEL: sudo yum install wget\n";
        echo "- macOS: brew install wget\n";
    }
}

echo "\n";

// Verificar sshpass (solo en Unix)
if (PHP_OS_FAMILY !== 'Windows') {
    echo "2. Verificando sshpass...\n";
    $output = [];
    exec('which sshpass 2>/dev/null', $output, $returnCode);
    
    if ($returnCode === 0 && !empty($output)) {
        echo "✅ sshpass está disponible: " . $output[0] . "\n";
    } else {
        echo "❌ sshpass no está disponible\n";
        echo "Instala con: sudo apt-get install sshpass\n";
    }
    echo "\n";
}

// Verificar SSH/SCP
echo "3. Verificando SSH/SCP...\n";
$sshCommands = ['ssh', 'scp', 'sftp'];

foreach ($sshCommands as $cmd) {
    $output = [];
    if (PHP_OS_FAMILY === 'Windows') {
        exec("{$cmd} 2>&1", $output, $returnCode);
        // En Windows, SSH puede estar disponible pero dar error sin argumentos
        $available = !empty($output) && (strpos(implode(' ', $output), 'usage') !== false || strpos(implode(' ', $output), 'OpenSSH') !== false);
    } else {
        exec("which {$cmd} 2>/dev/null", $output, $returnCode);
        $available = $returnCode === 0 && !empty($output);
    }
    
    if ($available) {
        echo "✅ {$cmd} está disponible\n";
    } else {
        echo "❌ {$cmd} no está disponible\n";
    }
}

echo "\n";

// Probar descarga simple con wget
echo "4. Probando descarga con wget...\n";
if ($returnCode === 0) { // Si wget está disponible
    $testUrl = 'https://httpbin.org/json';
    $tempFile = tempnam(sys_get_temp_dir(), 'wget_test');
    
    echo "Descargando archivo de prueba...\n";
    $command = "wget --timeout=10 --tries=1 -O " . escapeshellarg($tempFile) . " " . escapeshellarg($testUrl) . " 2>&1";
    
    $output = [];
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0 && file_exists($tempFile) && filesize($tempFile) > 0) {
        echo "✅ Descarga de prueba exitosa\n";
        echo "Archivo descargado: " . filesize($tempFile) . " bytes\n";
        unlink($tempFile);
    } else {
        echo "❌ Error en descarga de prueba\n";
        echo "Salida del comando:\n";
        foreach ($output as $line) {
            echo "  " . $line . "\n";
        }
        if (file_exists($tempFile)) {
            unlink($tempFile);
        }
    }
} else {
    echo "⏭️ Saltando prueba de descarga (wget no disponible)\n";
}

echo "\n";

// Verificar permisos de escritura
echo "5. Verificando permisos...\n";
$testDirs = ['./downloads', './uploads', sys_get_temp_dir()];

foreach ($testDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    if (is_writable($dir)) {
        echo "✅ {$dir} es escribible\n";
    } else {
        echo "❌ {$dir} no es escribible\n";
    }
}

echo "\n";

// Resumen
echo "=== RESUMEN ===\n";
echo "Sistema operativo: " . PHP_OS_FAMILY . "\n";
echo "Directorio temporal: " . sys_get_temp_dir() . "\n";

if (PHP_OS_FAMILY === 'Windows') {
    echo "\nPara Windows, asegúrate de tener:\n";
    echo "- wget instalado y en el PATH\n";
    echo "- PuTTY (plink) para conexiones SSH\n";
    echo "- OpenSSH Client (incluido en Windows 10+)\n";
} else {
    echo "\nPara Linux/macOS, asegúrate de tener:\n";
    echo "- wget\n";
    echo "- sshpass\n";
    echo "- openssh-client\n";
}

echo "\n¡Prueba completada!\n";
?>
