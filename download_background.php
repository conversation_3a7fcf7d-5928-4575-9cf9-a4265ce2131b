<?php
/**
 * Script para ejecutar descargas en segundo plano desde la interfaz web
 */

// Verificar argumentos
if ($argc < 2) {
    echo "Uso: php download_background.php <archivo_m3u> [serie_especifica]\n";
    exit(1);
}

$m3uFile = $argv[1];
$specificSeries = isset($argv[2]) && $argv[2] !== 'null' ? $argv[2] : null;

// Incluir la clase principal
require_once 'm3u_series_downloader.php';

try {
    // Crear instancia del descargador
    $downloader = new M3USeriesDownloader();
    
    // Parsear archivo M3U
    $downloader->log("=== INICIANDO DESCARGA DESDE INTERFAZ WEB ===");
    $downloader->log("Archivo M3U: {$m3uFile}");
    if ($specificSeries) {
        $downloader->log("Serie específica: {$specificSeries}");
    } else {
        $downloader->log("Descargando todas las series");
    }
    
    $entries = $downloader->parseM3U($m3uFile);
    
    if (empty($entries)) {
        $downloader->log("No se encontraron entradas válidas en el archivo M3U.", 'ERROR');
        exit(1);
    }
    
    // Identificar series
    $series = $downloader->identifySeries($entries);
    
    if (empty($series)) {
        $downloader->log("No se encontraron series con formato válido.", 'ERROR');
        exit(1);
    }
    
    // Conectar al servidor SFTP
    $downloader->log("Conectando al servidor SFTP...");
    $downloader->connectSFTP();
    
    // Iniciar descarga
    $downloader->log("Iniciando proceso de descarga...");
    $downloader->downloadSeries($series, $specificSeries);
    
    $downloader->log("=== DESCARGA COMPLETADA EXITOSAMENTE ===");
    
} catch (Exception $e) {
    if (isset($downloader)) {
        $downloader->log("ERROR FATAL: " . $e->getMessage(), 'ERROR');
    } else {
        error_log("ERROR FATAL: " . $e->getMessage());
    }
    exit(1);
} finally {
    // Desconectar
    if (isset($downloader)) {
        $downloader->disconnect();
    }
}
?>
