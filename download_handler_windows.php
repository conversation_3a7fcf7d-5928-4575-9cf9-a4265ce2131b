<?php
/**
 * Manejador de descargas optimizado para Windows
 * Usa PowerShell en lugar de wget
 */

session_start();
header('Content-Type: application/json');

require_once 'm3u_series_downloader.php';
require_once 'sftp_alternative.php';

class WindowsDownloadHandler {
    private $progressFile = './download_progress.json';
    private $pidFile = './download.pid';
    
    public function __construct() {
        if (!file_exists($this->progressFile)) {
            $this->saveProgress([
                'percent' => 0,
                'currentFile' => '',
                'speed' => '0 KB/s',
                'timeRemaining' => '--:--',
                'files' => [],
                'completed' => false,
                'error' => null
            ]);
        }
    }
    
    public function executeDownload() {
        try {
            if (!isset($_SESSION['sftp_config']) || !isset($_SESSION['series']) || !isset($_SESSION['download_config'])) {
                throw new Exception('Configuración incompleta');
            }
            
            $sftpConfig = $_SESSION['sftp_config'];
            $series = $_SESSION['series'];
            $downloadConfig = $_SESSION['download_config'];
            
            // Crear script de descarga para Windows
            $scriptPath = $this->createWindowsDownloadScript($sftpConfig, $series, $downloadConfig);
            
            // Ejecutar en segundo plano usando PowerShell
            $command = "powershell -WindowStyle Hidden -ExecutionPolicy Bypass -File \"{$scriptPath}\" > download_output.log 2>&1";
            $process = popen($command, 'r');
            
            if ($process) {
                // Obtener PID del proceso (aproximado)
                $pid = getmypid() + 1; // Estimación
                file_put_contents($this->pidFile, $pid);
                pclose($process);
                
                return ['success' => true, 'pid' => $pid];
            } else {
                throw new Exception('No se pudo iniciar el proceso de descarga');
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    private function createWindowsDownloadScript($sftpConfig, $series, $downloadConfig) {
        $scriptPath = './download_script.ps1';
        $downloadPath = $downloadConfig['download_path'];
        $selectedSeries = $downloadConfig['selected_series'];
        
        // Preparar lista de archivos a descargar
        $filesToDownload = [];
        
        foreach ($series as $seriesName => $seasons) {
            if ($selectedSeries && $selectedSeries !== $seriesName) {
                continue;
            }
            
            foreach ($seasons as $seasonNum => $episodes) {
                foreach ($episodes as $episodeNum => $entry) {
                    $url = $entry['url'];
                    $title = $entry['title'];
                    
                    $extension = $this->getFileExtension($url);
                    $filename = sprintf("S%02dE%02d - %s.%s", 
                        $seasonNum, 
                        $episodeNum, 
                        $this->cleanFileName($title), 
                        $extension
                    );
                    
                    $seriesDir = $downloadPath . '/' . $this->cleanFileName($seriesName);
                    $seasonDir = $seriesDir . '/Temporada ' . $seasonNum;
                    
                    $filesToDownload[] = [
                        'url' => $url,
                        'filename' => $filename,
                        'local_path' => $seasonDir . '/' . $filename,
                        'series' => $seriesName,
                        'season' => $seasonNum,
                        'episode' => $episodeNum
                    ];
                }
            }
        }
        
        // Crear script PowerShell
        $script = $this->generatePowerShellScript($sftpConfig, $filesToDownload);
        file_put_contents($scriptPath, $script);
        
        return $scriptPath;
    }
    
    private function generatePowerShellScript($sftpConfig, $files) {
        $host = $sftpConfig['host'];
        $port = $sftpConfig['port'];
        $username = $sftpConfig['username'];
        $password = $sftpConfig['password'];
        
        $script = "# Script de descarga PowerShell generado automáticamente\n";
        $script .= "# Host: {$host}:{$port}\n";
        $script .= "# Usuario: {$username}\n\n";
        
        $script .= "\$ProgressFile = '" . str_replace('\\', '/', $this->progressFile) . "'\n";
        $script .= "\$TotalFiles = " . count($files) . "\n";
        $script .= "\$CurrentFile = 0\n\n";
        
        $script .= "# Función para actualizar progreso\n";
        $script .= "function Update-Progress {\n";
        $script .= "    param(\$FileName, \$Status)\n";
        $script .= "    \$Percent = [math]::Round((\$CurrentFile / \$TotalFiles) * 100, 2)\n";
        $script .= "    \$ProgressData = @{\n";
        $script .= "        percent = \$Percent\n";
        $script .= "        currentFile = \$FileName\n";
        $script .= "        speed = '0 KB/s'\n";
        $script .= "        timeRemaining = '--:--'\n";
        $script .= "        completed = \$false\n";
        $script .= "        files = @()\n";
        $script .= "    }\n";
        $script .= "    \$ProgressData | ConvertTo-Json | Out-File -FilePath \$ProgressFile -Encoding UTF8\n";
        $script .= "}\n\n";
        
        $script .= "# Función para crear directorio en SFTP\n";
        $script .= "function Create-SFTPDir {\n";
        $script .= "    param(\$RemoteDir)\n";
        $script .= "    try {\n";
        $script .= "        \$SftpCommands = \"mkdir `\"\$RemoteDir`\"`nquit\"\n";
        $script .= "        \$SftpCommands | sftp -P {$port} -o StrictHostKeyChecking=no {$username}@{$host}\n";
        $script .= "    } catch {\n";
        $script .= "        Write-Host \"Error creando directorio: \$RemoteDir\"\n";
        $script .= "    }\n";
        $script .= "}\n\n";
        
        $script .= "# Función para subir archivo\n";
        $script .= "function Upload-File {\n";
        $script .= "    param(\$LocalFile, \$RemoteFile)\n";
        $script .= "    try {\n";
        $script .= "        scp -P {$port} -o StrictHostKeyChecking=no \"\$LocalFile\" \"{$username}@{$host}:\$RemoteFile\"\n";
        $script .= "        return \$LASTEXITCODE -eq 0\n";
        $script .= "    } catch {\n";
        $script .= "        return \$false\n";
        $script .= "    }\n";
        $script .= "}\n\n";
        
        $script .= "Write-Host \"Iniciando descarga de \$TotalFiles archivos...\"\n\n";
        
        // Crear directorios únicos
        $directories = [];
        foreach ($files as $file) {
            $dir = dirname($file['local_path']);
            if (!in_array($dir, $directories)) {
                $directories[] = $dir;
            }
        }
        
        foreach ($directories as $dir) {
            $script .= "Write-Host \"Creando directorio: {$dir}\"\n";
            $script .= "Create-SFTPDir \"{$dir}\"\n\n";
        }
        
        // Descargar archivos
        foreach ($files as $index => $file) {
            $localTempFile = sys_get_temp_dir() . "\\download_" . basename($file['filename']);
            
            $script .= "# Archivo " . ($index + 1) . " de " . count($files) . ": {$file['filename']}\n";
            $script .= "\$CurrentFile = " . ($index + 1) . "\n";
            $script .= "Update-Progress \"{$file['filename']}\" \"downloading\"\n";
            $script .= "Write-Host \"[$(Get-Date)] Descargando: {$file['filename']}\"\n";
            
            // Usar Invoke-WebRequest para descargar
            $script .= "try {\n";
            $script .= "    \$ProgressPreference = 'SilentlyContinue'\n";
            $script .= "    Invoke-WebRequest -Uri \"{$file['url']}\" -OutFile \"{$localTempFile}\" -TimeoutSec 300 -UserAgent 'M3U-Downloader/1.0'\n";
            $script .= "    \$ProgressPreference = 'Continue'\n";
            $script .= "    \n";
            $script .= "    if (Test-Path \"{$localTempFile}\") {\n";
            $script .= "        Write-Host \"[$(Get-Date)] ✅ Descarga completada: {$file['filename']}\"\n";
            $script .= "        Write-Host \"[$(Get-Date)] 📤 Subiendo a servidor SFTP...\"\n";
            $script .= "        \n";
            $script .= "        if (Upload-File \"{$localTempFile}\" \"{$file['local_path']}\") {\n";
            $script .= "            Write-Host \"[$(Get-Date)] ✅ Subida completada: {$file['filename']}\"\n";
            $script .= "            Remove-Item \"{$localTempFile}\" -Force\n";
            $script .= "        } else {\n";
            $script .= "            Write-Host \"[$(Get-Date)] ❌ Error subiendo: {$file['filename']}\"\n";
            $script .= "        }\n";
            $script .= "    } else {\n";
            $script .= "        Write-Host \"[$(Get-Date)] ❌ Archivo no descargado: {$file['filename']}\"\n";
            $script .= "    }\n";
            $script .= "} catch {\n";
            $script .= "    Write-Host \"[$(Get-Date)] ❌ Error descargando: {$file['filename']} - \$(\$_.Exception.Message)\"\n";
            $script .= "}\n\n";
        }
        
        $script .= "# Marcar como completado\n";
        $script .= "\$FinalProgress = @{\n";
        $script .= "    percent = 100\n";
        $script .= "    currentFile = 'Descarga completada'\n";
        $script .= "    speed = '0 KB/s'\n";
        $script .= "    timeRemaining = '00:00'\n";
        $script .= "    completed = \$true\n";
        $script .= "    files = @()\n";
        $script .= "}\n";
        $script .= "\$FinalProgress | ConvertTo-Json | Out-File -FilePath \$ProgressFile -Encoding UTF8\n\n";
        
        $script .= "Write-Host \"🎉 Descarga completada!\"\n";
        
        return $script;
    }
    
    private function getFileExtension($url) {
        $path = parse_url($url, PHP_URL_PATH);
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        return $extension ?: 'mp4';
    }
    
    private function cleanFileName($name) {
        $name = preg_replace('/[<>:"/\\|?*]/', '', $name);
        $name = trim($name, '. ');
        return substr($name, 0, 100);
    }
    
    public function pauseDownload() {
        // En Windows, pausar es más complejo, por ahora solo marcar como pausado
        file_put_contents('./download_status.txt', 'paused');
        return ['success' => true, 'message' => 'Descarga marcada como pausada'];
    }
    
    public function resumeDownload() {
        file_put_contents('./download_status.txt', 'active');
        return ['success' => true, 'message' => 'Descarga marcada como activa'];
    }
    
    public function cancelDownload() {
        if (file_exists($this->pidFile)) {
            $pid = file_get_contents($this->pidFile);
            exec("taskkill /F /PID {$pid} 2>nul");
            unlink($this->pidFile);
        }
        
        $this->saveProgress([
            'percent' => 0,
            'currentFile' => 'Descarga cancelada',
            'completed' => true,
            'error' => 'Cancelado por el usuario'
        ]);
        
        file_put_contents('./download_status.txt', 'cancelled');
        return ['success' => true, 'message' => 'Descarga cancelada'];
    }
    
    private function saveProgress($data) {
        file_put_contents($this->progressFile, json_encode($data, JSON_PRETTY_PRINT));
    }
}

// Procesar solicitud
$handler = new WindowsDownloadHandler();
$action = $_POST['action'] ?? '';

switch ($action) {
    case 'execute_download':
        echo json_encode($handler->executeDownload());
        break;
        
    case 'pause':
        echo json_encode($handler->pauseDownload());
        break;
        
    case 'resume':
        echo json_encode($handler->resumeDownload());
        break;
        
    case 'cancel':
        echo json_encode($handler->cancelDownload());
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Acción no válida']);
}
?>
