<?php
/**
 * Script para probar el explorador de directorios
 */

echo "=== PRUEBA DEL EXPLORADOR DE DIRECTORIOS ===\n\n";

// Simular una sesión con configuración SFTP
session_start();

// Configuración de prueba (cambiar por tus datos reales)
$_SESSION['sftp_config'] = [
    'host' => 'test.rebex.net',  // Servidor SFTP público de prueba
    'port' => 22,
    'username' => 'demo',
    'password' => 'password'
];

echo "Usando servidor de prueba público:\n";
echo "Host: test.rebex.net\n";
echo "Usuario: demo\n";
echo "Contraseña: password\n\n";

echo "NOTA: Este es un servidor público de prueba. Para usar tu servidor real,\n";
echo "modifica las credenciales en test_directory_browser.php\n\n";

// Simular petición POST
$_POST['action'] = 'list_directories';
$_POST['path'] = '/';

echo "Probando listado de directorio raíz...\n";

// Capturar la salida del script
ob_start();
include 'simple_directory_browser.php';
$output = ob_get_clean();

echo "Respuesta del servidor:\n";
echo $output . "\n\n";

// Decodificar JSON para mostrar de forma legible
$data = json_decode($output, true);

if ($data && $data['success']) {
    echo "✅ Explorador funcionando correctamente!\n";
    echo "Directorios encontrados:\n";
    
    foreach ($data['directories'] as $item) {
        $icon = $item['type'] === 'directory' ? '📁' : '📄';
        echo "  {$icon} {$item['name']} ({$item['type']})\n";
    }
} else {
    echo "❌ Error en el explorador:\n";
    if ($data && isset($data['message'])) {
        echo "Mensaje: " . $data['message'] . "\n";
    }
}

echo "\n=== PRUEBA DE RUTAS ACCESIBLES ===\n\n";

// Probar rutas accesibles
$_POST['action'] = 'test_paths';
unset($_POST['path']);

ob_start();
include 'simple_directory_browser.php';
$output = ob_get_clean();

echo "Respuesta del servidor:\n";
echo $output . "\n\n";

$data = json_decode($output, true);

if ($data && $data['success']) {
    echo "✅ Prueba de rutas completada!\n";
    echo "Rutas accesibles:\n";
    
    foreach ($data['accessiblePaths'] as $path) {
        echo "  📁 {$path}\n";
    }
} else {
    echo "❌ Error probando rutas:\n";
    if ($data && isset($data['message'])) {
        echo "Mensaje: " . $data['message'] . "\n";
    }
}

echo "\n=== INSTRUCCIONES ===\n\n";

echo "Para usar con tu servidor real:\n";
echo "1. Modifica las credenciales en \$_SESSION['sftp_config']\n";
echo "2. Ejecuta: php test_directory_browser.php\n";
echo "3. Si funciona aquí, funcionará en la interfaz web\n\n";

echo "Para probar en la interfaz web:\n";
echo "1. Ve a http://localhost:8000\n";
echo "2. Configura tu conexión SFTP\n";
echo "3. Haz clic en 'Explorar Servidor'\n";
echo "4. Si no carga, haz clic en '🔍 Buscar Rutas'\n\n";

echo "Comandos necesarios en tu sistema:\n";
if (PHP_OS_FAMILY === 'Windows') {
    echo "- plink (PuTTY) o OpenSSH Client\n";
    echo "- Instalar con: winget install PuTTY.PuTTY\n";
} else {
    echo "- sshpass: sudo apt-get install sshpass\n";
    echo "- sftp: sudo apt-get install openssh-client\n";
}

echo "\n¡Prueba completada!\n";
?>
