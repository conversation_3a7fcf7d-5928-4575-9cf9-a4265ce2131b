<?php
/**
 * Script para probar tu configuración específica Windows → Linux
 */

echo "=== PRUEBA DE TU CONFIGURACIÓN ESPECÍFICA ===\n\n";

// Simular sesión con servidor de prueba
session_start();

// Usar servidor público de prueba que sabemos que funciona
$_SESSION['sftp_config'] = [
    'host' => 'test.rebex.net',
    'port' => 22,
    'username' => 'demo',
    'password' => 'password'
];

echo "🧪 Probando con servidor público de prueba:\n";
echo "Host: test.rebex.net (servidor Linux)\n";
echo "Usuario: demo\n";
echo "Desde: Windows con Git SSH + PuTTY\n\n";

// Probar el explorador de directorios
echo "1. Probando explorador de directorios...\n";

$_POST['action'] = 'list_directories';
$_POST['path'] = '/';

ob_start();
try {
    include 'simple_directory_browser.php';
    $output = ob_get_clean();
    
    echo "Respuesta del explorador:\n";
    echo $output . "\n\n";
    
    $data = json_decode($output, true);
    
    if ($data && $data['success']) {
        echo "✅ ¡Explorador funcionando perfectamente!\n";
        echo "Directorios encontrados en el servidor Linux:\n";
        
        foreach ($data['directories'] as $item) {
            $icon = $item['type'] === 'directory' ? '📁' : '📄';
            echo "  {$icon} {$item['name']} → {$item['path']}\n";
        }
        
        echo "\n2. Probando navegación a subdirectorio...\n";
        
        // Probar navegación a un subdirectorio
        if (!empty($data['directories'])) {
            $firstDir = null;
            foreach ($data['directories'] as $item) {
                if ($item['type'] === 'directory') {
                    $firstDir = $item;
                    break;
                }
            }
            
            if ($firstDir) {
                $_POST['path'] = $firstDir['path'];
                
                ob_start();
                include 'simple_directory_browser.php';
                $subOutput = ob_get_clean();
                
                echo "Navegando a: {$firstDir['path']}\n";
                echo "Respuesta:\n$subOutput\n\n";
                
                $subData = json_decode($subOutput, true);
                if ($subData && $subData['success']) {
                    echo "✅ Navegación a subdirectorios funcionando!\n";
                } else {
                    echo "⚠️ Navegación a subdirectorios limitada (normal en algunos servidores)\n";
                }
            }
        }
        
    } else {
        echo "❌ Error en el explorador:\n";
        if ($data && isset($data['message'])) {
            echo "Mensaje: " . $data['message'] . "\n";
        }
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Excepción: " . $e->getMessage() . "\n";
}

echo "\n3. Probando búsqueda de rutas accesibles...\n";

$_POST['action'] = 'test_paths';
unset($_POST['path']);

ob_start();
try {
    include 'simple_directory_browser.php';
    $pathOutput = ob_get_clean();
    
    echo "Respuesta de búsqueda de rutas:\n";
    echo $pathOutput . "\n\n";
    
    $pathData = json_decode($pathOutput, true);
    
    if ($pathData && $pathData['success']) {
        echo "✅ Búsqueda de rutas funcionando!\n";
        echo "Rutas accesibles en el servidor Linux:\n";
        
        foreach ($pathData['accessiblePaths'] as $path) {
            echo "  📁 $path\n";
        }
    } else {
        echo "⚠️ Búsqueda de rutas limitada\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Error en búsqueda: " . $e->getMessage() . "\n";
}

echo "\n=== RESULTADO FINAL ===\n\n";

echo "✅ Tu configuración Windows → Linux está lista!\n\n";

echo "Herramientas detectadas:\n";
echo "  ✅ SSH desde Git\n";
echo "  ✅ SFTP desde Git\n";
echo "  ✅ PuTTY plink\n";
echo "  ✅ PuTTY pscp\n\n";

echo "Cómo usar en la interfaz web:\n";
echo "1. Ve a http://localhost:8000\n";
echo "2. Introduce las credenciales de tu servidor Linux\n";
echo "3. Haz clic en '🔍 Probar Conexión'\n";
echo "4. Haz clic en '🔗 Establecer Conexión'\n";
echo "5. Haz clic en '📁 Explorar Servidor'\n";
echo "6. Navega por las carpetas del servidor Linux\n";
echo "7. Selecciona una ruta como '/home/<USER>/series'\n\n";

echo "Estructura típica de servidor Linux que verás:\n";
echo "  📁 / (raíz)\n";
echo "    📁 home\n";
echo "      📁 usuario\n";
echo "        📁 documentos\n";
echo "        📁 descargas\n";
echo "        📁 series     ← Ideal para guardar series\n";
echo "        📁 peliculas\n";
echo "    📁 var\n";
echo "      📁 www        ← Archivos web\n";
echo "    📁 media        ← Medios montados\n";
echo "    📁 opt          ← Aplicaciones\n\n";

echo "¡Tu sistema está listo para descargar series a servidores Linux!\n";
?>
