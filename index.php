<?php
/**
 * Interfaz Web para el Descargador de Series M3U con SFTP
 */

session_start();

// Incluir las clases principales
require_once 'm3u_series_downloader.php';
require_once 'sftp_alternative.php';

// Función para manejar la subida de archivos
function handleFileUpload() {
    if (isset($_FILES['m3u_file']) && $_FILES['m3u_file']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = './uploads/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $fileName = basename($_FILES['m3u_file']['name']);
        $targetPath = $uploadDir . $fileName;

        if (move_uploaded_file($_FILES['m3u_file']['tmp_name'], $targetPath)) {
            return $targetPath;
        }
    }
    return false;
}

// Función para probar conexión SFTP
function testSFTPConnection($host, $port, $username, $password) {
    try {
        // Intentar usar SSH2 si está disponible
        if (extension_loaded('ssh2')) {
            $connection = ssh2_connect($host, $port);
            if (!$connection) {
                return ['success' => false, 'message' => 'No se pudo conectar al servidor ' . $host . ':' . $port];
            }

            $auth = ssh2_auth_password($connection, $username, $password);
            if (!$auth) {
                return ['success' => false, 'message' => 'Falló la autenticación para usuario: ' . $username];
            }

            $sftp = ssh2_sftp($connection);
            if (!$sftp) {
                return ['success' => false, 'message' => 'No se pudo inicializar SFTP'];
            }

            ssh2_disconnect($connection);
            return ['success' => true, 'message' => 'Conexión exitosa (usando SSH2)'];
        } else {
            // Usar alternativa con comandos del sistema
            $sftp = new SFTPAlternative($host, $port, $username, $password);
            return $sftp->testConnection();
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    } catch (Error $e) {
        return ['success' => false, 'message' => 'Error PHP: ' . $e->getMessage()];
    }
}

// Función para listar directorios remotos
function listRemoteDirectories($host, $port, $username, $password, $path = '/') {
    try {
        // Intentar usar SSH2 si está disponible
        if (extension_loaded('ssh2')) {
            $connection = ssh2_connect($host, $port);
            if (!$connection) return false;

            $auth = ssh2_auth_password($connection, $username, $password);
            if (!$auth) return false;

            $sftp = ssh2_sftp($connection);
            if (!$sftp) return false;

            // Normalizar la ruta
            $path = rtrim($path, '/') . '/';
            if ($path === '//') $path = '/';

            $handle = @opendir("ssh2.sftp://{$sftp}{$path}");
            if (!$handle) {
                ssh2_disconnect($connection);
                return false;
            }

            $directories = [];
            $files = [];

            while (($file = readdir($handle)) !== false) {
                if ($file === '.' || $file === '..') continue;

                $fullPath = $path . $file;
                if (@is_dir("ssh2.sftp://{$sftp}{$fullPath}")) {
                    $directories[] = [
                        'name' => $file,
                        'path' => $fullPath,
                        'type' => 'directory'
                    ];
                } else {
                    $files[] = [
                        'name' => $file,
                        'path' => $fullPath,
                        'type' => 'file'
                    ];
                }
            }

            closedir($handle);
            ssh2_disconnect($connection);

            // Ordenar directorios primero, luego archivos
            usort($directories, function($a, $b) { return strcasecmp($a['name'], $b['name']); });
            usort($files, function($a, $b) { return strcasecmp($a['name'], $b['name']); });

            return array_merge($directories, $files);
        } else {
            // Usar alternativa con comandos del sistema
            $sftp = new SFTPAlternative($host, $port, $username, $password);
            return $sftp->listDirectory($path);
        }
    } catch (Exception $e) {
        return false;
    } catch (Error $e) {
        return false;
    }
}

// Procesar formularios
$message = '';
$messageType = '';
$series = [];
$m3uFile = '';
$sftpStatus = null;
$remoteDirectories = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'test_sftp':
                    $host = trim($_POST['sftp_host']);
                    $port = intval($_POST['sftp_port']);
                    $username = trim($_POST['sftp_username']);
                    $password = $_POST['sftp_password'];

                    $sftpStatus = testSFTPConnection($host, $port, $username, $password);

                    if ($sftpStatus['success']) {
                        // Solo guardar temporalmente para la prueba
                        $_SESSION['sftp_test'] = [
                            'host' => $host,
                            'port' => $port,
                            'username' => $username,
                            'password' => $password
                        ];
                        $message = "✅ Conexión SFTP exitosa. Ahora puedes establecer la conexión.";
                        $messageType = 'success';
                    } else {
                        $message = "❌ Error de conexión SFTP: " . $sftpStatus['message'];
                        $messageType = 'error';
                    }
                    break;

                case 'establish_sftp':
                    if (isset($_SESSION['sftp_test'])) {
                        $_SESSION['sftp_config'] = $_SESSION['sftp_test'];
                        unset($_SESSION['sftp_test']);
                        $message = "🔗 Conexión SFTP establecida correctamente.";
                        $messageType = 'success';

                        // Inicializar explorador en el directorio home del usuario remoto
                        $_SESSION['current_browse_path'] = '/';

                        // Listar directorios remotos para el explorador
                        $config = $_SESSION['sftp_config'];
                        $remoteDirectories = listRemoteDirectories(
                            $config['host'],
                            $config['port'],
                            $config['username'],
                            $config['password'],
                            '/'
                        );

                        // Si no se puede acceder a /, intentar con el directorio home
                        if ($remoteDirectories === false) {
                            $homeDir = '/home/' . $config['username'];
                            $remoteDirectories = listRemoteDirectories(
                                $config['host'],
                                $config['port'],
                                $config['username'],
                                $config['password'],
                                $homeDir
                            );
                            if ($remoteDirectories !== false) {
                                $_SESSION['current_browse_path'] = $homeDir;
                            }
                        }
                    } else {
                        $message = "❌ Debes probar la conexión primero.";
                        $messageType = 'error';
                    }
                    break;

                case 'browse_directory':
                    if (isset($_SESSION['sftp_config'])) {
                        $browsePath = $_POST['browse_path'] ?? '/';
                        $config = $_SESSION['sftp_config'];
                        $remoteDirectories = listRemoteDirectories(
                            $config['host'],
                            $config['port'],
                            $config['username'],
                            $config['password'],
                            $browsePath
                        );
                        $_SESSION['current_browse_path'] = $browsePath;
                    }
                    break;

                case 'upload_m3u':
                    $m3uFile = handleFileUpload();
                    if ($m3uFile) {
                        $_SESSION['m3u_file'] = $m3uFile;
                        $downloader = new M3USeriesDownloader();
                        $entries = $downloader->parseM3U($m3uFile);
                        $series = $downloader->identifySeries($entries);
                        $_SESSION['series'] = $series;
                        $message = "Archivo M3U procesado exitosamente. Encontradas " . count($series) . " series.";
                        $messageType = 'success';
                    } else {
                        $message = "Error al subir el archivo M3U.";
                        $messageType = 'error';
                    }
                    break;

                case 'configure_download':
                    if (isset($_SESSION['series']) && isset($_POST['selected_series']) && isset($_SESSION['sftp_config'])) {
                        $selectedSeries = $_POST['selected_series'] === 'all' ? null : $_POST['selected_series'];
                        $downloadPath = $_POST['download_path'];

                        // Validar que la ruta no esté vacía
                        if (empty($downloadPath)) {
                            $message = "❌ Debes seleccionar una ruta de descarga.";
                            $messageType = 'error';
                            break;
                        }

                        // Guardar configuración de descarga
                        $_SESSION['download_config'] = [
                            'selected_series' => $selectedSeries,
                            'download_path' => $downloadPath
                        ];

                        $seriesText = $selectedSeries ? $selectedSeries : 'todas las series';
                        $message = "✅ Descarga configurada: {$seriesText} en {$downloadPath}";
                        $messageType = 'success';
                    } else {
                        $message = "❌ Debes configurar la conexión SFTP y subir un archivo M3U primero.";
                        $messageType = 'error';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $messageType = 'error';
    }
}

// Recuperar datos de sesión
if (isset($_SESSION['series'])) {
    $series = $_SESSION['series'];
}
if (isset($_SESSION['m3u_file'])) {
    $m3uFile = $_SESSION['m3u_file'];
}

$sftpTested = isset($_SESSION['sftp_test']);
$sftpConnected = isset($_SESSION['sftp_config']);
$downloadReady = isset($_SESSION['download_config']);

// Inicializar ruta de navegación remota
if ($sftpConnected && !isset($_SESSION['current_browse_path'])) {
    $_SESSION['current_browse_path'] = '/';
}
$currentBrowsePath = $_SESSION['current_browse_path'] ?? '/';
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Descargador de Series M3U</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .series-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .series-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s;
        }
        
        .series-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .series-card.selected {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .series-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .series-info {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .progress-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .log-viewer {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .connection-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .download-progress {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .file-list {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-status {
            font-size: 0.9em;
            font-weight: bold;
        }

        .status-downloading {
            color: #007bff;
        }

        .status-completed {
            color: #28a745;
        }

        .status-error {
            color: #dc3545;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }

        .directory-browser {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }

        .directory-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .directory-item:hover {
            background-color: #e9ecef;
        }

        .directory-item:last-child {
            border-bottom: none;
        }

        .directory-item.selected {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }

        .directory-icon {
            margin-right: 10px;
            font-size: 1.2em;
        }

        .directory-name {
            flex: 1;
            font-weight: 500;
        }

        .breadcrumb {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .path-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }

        .path-input-group input {
            flex: 1;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Descargador de Series M3U</h1>
            <p>Organiza y descarga tus series favoritas automáticamente</p>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Estado de Conexión -->
            <div class="card">
                <h3>🌐 Configuración de Conexión SFTP</h3>
                <div style="margin-bottom: 15px;">
                    <span class="connection-status <?php echo $sftpConnected ? 'status-connected' : ($sftpTested ? 'status-warning' : 'status-disconnected'); ?>">
                        <?php
                        if ($sftpConnected) {
                            echo '✅ Conectado y Establecido';
                        } elseif ($sftpTested) {
                            echo '⚠️ Probado - Listo para Establecer';
                        } else {
                            echo '❌ Sin Configurar';
                        }
                        ?>
                    </span>
                    <?php if ($sftpConnected): ?>
                        <span style="margin-left: 15px; color: #6c757d;">
                            Servidor: <?php echo htmlspecialchars($_SESSION['sftp_config']['host']); ?>
                        </span>
                    <?php endif; ?>
                </div>

                <?php if (!$sftpConnected): ?>
                    <?php if (!$sftpTested): ?>
                    <!-- Formulario para probar conexión -->
                    <form method="post" class="two-column">
                        <input type="hidden" name="action" value="test_sftp">
                        <div>
                            <div class="form-group">
                                <label for="sftp_host">Servidor SFTP:</label>
                                <input type="text" name="sftp_host" id="sftp_host" class="form-control"
                                       placeholder="ejemplo.com o *************"
                                       value="<?php echo isset($_SESSION['sftp_test']) ? htmlspecialchars($_SESSION['sftp_test']['host']) : ''; ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="sftp_port">Puerto:</label>
                                <input type="number" name="sftp_port" id="sftp_port" class="form-control"
                                       value="<?php echo isset($_SESSION['sftp_test']) ? $_SESSION['sftp_test']['port'] : '22'; ?>" required>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label for="sftp_username">Usuario:</label>
                                <input type="text" name="sftp_username" id="sftp_username" class="form-control"
                                       value="<?php echo isset($_SESSION['sftp_test']) ? htmlspecialchars($_SESSION['sftp_test']['username']) : ''; ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="sftp_password">Contraseña:</label>
                                <input type="password" name="sftp_password" id="sftp_password" class="form-control" required>
                            </div>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <button type="submit" class="btn">🔍 Probar Conexión</button>
                        </div>
                    </form>
                    <?php else: ?>
                    <!-- Conexión probada exitosamente -->
                    <div class="alert alert-success">
                        ✅ Conexión probada exitosamente con el servidor:
                        <strong><?php echo htmlspecialchars($_SESSION['sftp_test']['host']); ?></strong>
                    </div>
                    <form method="post">
                        <input type="hidden" name="action" value="establish_sftp">
                        <button type="submit" class="btn btn-success">🔗 Establecer Conexión</button>
                        <a href="?reset_test=1" class="btn btn-danger" style="margin-left: 10px;">🔄 Probar Otra Conexión</a>
                    </form>
                    <?php endif; ?>
                <?php else: ?>
                <div class="alert alert-success">
                    ✅ Conexión SFTP establecida correctamente con:
                    <strong><?php echo htmlspecialchars($_SESSION['sftp_config']['host']); ?></strong>
                </div>
                <a href="?disconnect=1" class="btn btn-danger">🔌 Desconectar</a>
                <?php endif; ?>
            </div>

            <!-- Paso 1: Subir archivo M3U -->
            <?php if ($sftpConnected): ?>
            <div class="card">
                <h3>📁 Paso 1: Subir Archivo M3U</h3>
                <form method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="upload_m3u">
                    <div class="form-group">
                        <label for="m3u_file">Selecciona tu archivo M3U:</label>
                        <input type="file" name="m3u_file" id="m3u_file" class="form-control" accept=".m3u,.m3u8" required>
                    </div>
                    <button type="submit" class="btn">📤 Subir y Analizar</button>
                </form>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($series)): ?>
                <!-- Estadísticas -->
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($series); ?></div>
                        <div class="stat-label">Series Encontradas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">
                            <?php 
                            $totalSeasons = 0;
                            foreach ($series as $seasons) {
                                $totalSeasons += count($seasons);
                            }
                            echo $totalSeasons;
                            ?>
                        </div>
                        <div class="stat-label">Temporadas Totales</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">
                            <?php 
                            $totalEpisodes = 0;
                            foreach ($series as $seasons) {
                                foreach ($seasons as $episodes) {
                                    $totalEpisodes += count($episodes);
                                }
                            }
                            echo $totalEpisodes;
                            ?>
                        </div>
                        <div class="stat-label">Episodios Totales</div>
                    </div>
                </div>
                
                <!-- Paso 2: Seleccionar Ruta de Descarga en Servidor Remoto -->
                <div class="card">
                    <h3>📂 Paso 2: Seleccionar Ruta de Descarga en Servidor Remoto</h3>

                    <?php if ($sftpConnected): ?>
                    <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin-bottom: 15px; border-left: 4px solid #007bff;">
                        <strong>🌐 Servidor:</strong> <?php echo htmlspecialchars($_SESSION['sftp_config']['host']); ?><br>
                        <strong>👤 Usuario:</strong> <?php echo htmlspecialchars($_SESSION['sftp_config']['username']); ?>
                    </div>

                    <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin-bottom: 15px; border-left: 4px solid #007bff;">
                        <strong>🌐 Hostinger → Servidor Remoto:</strong> Navegarás por las carpetas del servidor remoto donde se guardarán las series descargadas.
                        <br><small>Ejecutándose desde: <?php echo php_uname('n'); ?> (<?php echo PHP_OS; ?>)</small>
                    </div>
                    <?php endif; ?>

                    <div class="path-input-group">
                        <input type="text" id="selected_path" class="form-control"
                               placeholder="/home/<USER>/series/"
                               value="<?php echo htmlspecialchars($currentBrowsePath); ?>" readonly>
                        <button type="button" onclick="openDirectoryBrowser()" class="btn">📁 Explorar Servidor</button>
                        <?php if ($sftpConnected): ?>
                        <button type="button" onclick="goToHome()" class="btn" style="background: #17a2b8; margin-left: 5px;">🏠 Ir a Home</button>
                        <?php endif; ?>
                    </div>

                    <!-- Explorador de Directorios del Servidor Remoto -->
                    <div id="directoryBrowser" style="display: none;">
                        <div class="breadcrumb">
                            <strong>🌐 Navegando en servidor remoto:</strong><br>
                            <strong>Ruta actual:</strong>
                            <span id="currentPath"><?php echo htmlspecialchars($currentBrowsePath); ?></span>
                        </div>

                        <div class="directory-browser" id="directoryList">
                            <div class="directory-item">
                                <span class="directory-icon">⏳</span>
                                <span class="directory-name">Haz clic en "📁 Explorar Servidor" para cargar directorios</span>
                            </div>
                        </div>

                        <div style="margin-top: 15px; text-align: center;">
                            <button type="button" onclick="selectCurrentPath()" class="btn btn-success">✅ Seleccionar Esta Ruta</button>
                            <button type="button" onclick="refreshDirectory()" class="btn" style="background: #ffc107; margin: 0 5px;">🔄 Actualizar</button>
                            <button type="button" onclick="showAccessiblePaths()" class="btn" style="background: #17a2b8; margin: 0 5px;">🔍 Buscar Rutas</button>
                            <button type="button" onclick="closeDirectoryBrowser()" class="btn btn-danger">❌ Cancelar</button>
                        </div>
                    </div>
                </div>

                <!-- Paso 3: Seleccionar Series -->
                <div class="card">
                    <h3>🎯 Paso 3: Seleccionar Series para Descargar</h3>
                    <form method="post" id="downloadForm">
                        <input type="hidden" name="action" value="configure_download">
                        <input type="hidden" name="download_path" id="download_path_hidden" value="<?php echo htmlspecialchars($currentBrowsePath); ?>">

                        <div class="form-group">
                            <label>
                                <input type="radio" name="selected_series" value="all" checked>
                                <strong>Descargar todas las series</strong>
                            </label>
                        </div>

                        <div class="series-grid">
                            <?php foreach ($series as $seriesName => $seasons): ?>
                                <div class="series-card" onclick="selectSeries('<?php echo htmlspecialchars($seriesName); ?>')">
                                    <label>
                                        <input type="radio" name="selected_series" value="<?php echo htmlspecialchars($seriesName); ?>">
                                        <div class="series-title"><?php echo htmlspecialchars($seriesName); ?></div>
                                    </label>
                                    <div class="series-info">
                                        <?php
                                        $episodeCount = 0;
                                        foreach ($seasons as $episodes) {
                                            $episodeCount += count($episodes);
                                        }
                                        echo count($seasons) . " temporadas • " . $episodeCount . " episodios";
                                        ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div style="margin-top: 30px;">
                            <button type="submit" class="btn btn-success">⚙️ Configurar Descarga</button>
                        </div>
                    </form>

                    <?php if ($downloadReady): ?>
                    <div style="margin-top: 20px; padding: 15px; background: #d4edda; border-radius: 8px; border: 1px solid #c3e6cb;">
                        <h4 style="color: #155724; margin-bottom: 10px;">✅ Descarga Lista</h4>
                        <p style="color: #155724; margin-bottom: 15px;">
                            <strong>Serie:</strong> <?php echo $_SESSION['download_config']['selected_series'] ?: 'Todas las series'; ?><br>
                            <strong>Ruta:</strong> <?php echo htmlspecialchars($_SESSION['download_config']['download_path']); ?>
                        </p>
                        <button type="button" onclick="startDownload()" class="btn" style="background: #28a745;">🚀 Iniciar Descarga Ahora</button>
                    </div>
                    <?php endif; ?>
                    </form>
                </div>
            <?php endif; ?>
            
            <!-- Monitor de Progreso -->
            <div class="card">
                <h3>📊 Monitor de Progreso</h3>
                <div class="progress-container">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <div>
                            <button onclick="loadLog()" class="btn">🔄 Actualizar Log</button>
                            <button onclick="toggleAutoRefresh()" class="btn" id="autoRefreshBtn" style="margin-left: 10px;">▶️ Auto-actualizar</button>
                        </div>
                        <div id="downloadControls" style="display: none;">
                            <button onclick="pauseDownload()" class="btn btn-warning" id="pauseBtn">⏸️ Pausar</button>
                            <button onclick="cancelDownload()" class="btn btn-danger" id="cancelBtn" style="margin-left: 10px;">❌ Cancelar</button>
                        </div>
                    </div>

                    <!-- Barra de progreso general -->
                    <div id="progressSection" style="display: none; margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                            <span id="progressText">Preparando descarga...</span>
                            <span id="progressPercent">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div id="progressFill" class="progress-fill"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 0.9em; color: #6c757d; margin-top: 5px;">
                            <span id="downloadSpeed">0 KB/s</span>
                            <span id="timeRemaining">--:--</span>
                        </div>
                    </div>

                    <div id="logContent" class="log-viewer">
                        No hay actividad de descarga. Configura y inicia una descarga para ver el progreso aquí...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Descarga -->
    <div id="downloadModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🚀 Descarga en Progreso</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>

            <div class="download-progress">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <span id="currentFile">Preparando descarga...</span>
                    <span id="progressPercent">0%</span>
                </div>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 0.9em; color: #6c757d;">
                    <span id="downloadSpeed">0 KB/s</span>
                    <span id="timeRemaining">--:--</span>
                </div>
            </div>

            <div class="file-list" id="fileList">
                <div class="file-item">
                    <span>Iniciando proceso de descarga...</span>
                    <span class="file-status status-downloading">⏳ Preparando</span>
                </div>
            </div>

            <div style="margin-top: 20px; text-align: center;">
                <button onclick="pauseDownload()" class="btn btn-warning" id="pauseBtn">⏸️ Pausar</button>
                <button onclick="cancelDownload()" class="btn btn-danger" id="cancelBtn">❌ Cancelar</button>
                <button onclick="closeModal()" class="btn" id="closeBtn" style="display: none;">✅ Cerrar</button>
            </div>
        </div>
    </div>
    
    <script>
        let downloadProcess = null;
        let downloadInterval = null;
        let autoRefreshInterval = null;
        let isAutoRefreshing = false;

        function selectSeries(seriesName) {
            // Remover selección previa
            document.querySelectorAll('.series-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Seleccionar la nueva serie
            event.currentTarget.classList.add('selected');
        }

        function startDownload() {
            // Mostrar controles de descarga y progreso
            document.getElementById('downloadControls').style.display = 'block';
            document.getElementById('progressSection').style.display = 'block';

            // Iniciar auto-refresh si no está activo
            if (!isAutoRefreshing) {
                toggleAutoRefresh();
            }

            // Iniciar descarga usando el manejador apropiado para el sistema
            const formData = new FormData();
            formData.append('action', 'execute_download');

            // Detectar sistema operativo y usar el manejador apropiado
            const isWindows = navigator.platform.indexOf('Win') > -1;
            const handlerUrl = isWindows ? 'download_handler_windows.php' : 'download_handler.php';

            fetch(handlerUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('progressText').textContent = 'Descarga iniciada...';
                    // Iniciar monitoreo del progreso
                    startProgressMonitoring();
                } else {
                    alert('Error iniciando descarga: ' + data.message);
                    hideDownloadControls();
                }
            })
            .catch(error => {
                alert('Error: ' + error);
                hideDownloadControls();
            });
        }

        function hideDownloadControls() {
            document.getElementById('downloadControls').style.display = 'none';
            document.getElementById('progressSection').style.display = 'none';
        }

        function startProgressMonitoring() {
            downloadInterval = setInterval(function() {
                fetch('download_progress.php')
                    .then(response => response.json())
                    .then(data => {
                        updateProgress(data);

                        if (data.completed) {
                            clearInterval(downloadInterval);
                            document.getElementById('pauseBtn').style.display = 'none';
                            document.getElementById('cancelBtn').style.display = 'none';
                            document.getElementById('closeBtn').style.display = 'inline-block';
                        }
                    })
                    .catch(error => {
                        console.error('Error obteniendo progreso:', error);
                    });
            }, 2000); // Actualizar cada 2 segundos
        }

        function updateProgress(data) {
            // Actualizar barra de progreso
            document.getElementById('progressFill').style.width = data.percent + '%';
            document.getElementById('progressPercent').textContent = data.percent + '%';

            // Actualizar archivo actual
            document.getElementById('currentFile').textContent = data.currentFile || 'Procesando...';

            // Actualizar velocidad y tiempo
            document.getElementById('downloadSpeed').textContent = data.speed || '0 KB/s';
            document.getElementById('timeRemaining').textContent = data.timeRemaining || '--:--';

            // Actualizar lista de archivos
            if (data.files) {
                updateFileList(data.files);
            }
        }

        function updateFileList(files) {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';

            files.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';

                let statusClass = 'status-downloading';
                let statusIcon = '⏳';

                if (file.status === 'completed') {
                    statusClass = 'status-completed';
                    statusIcon = '✅';
                } else if (file.status === 'error') {
                    statusClass = 'status-error';
                    statusIcon = '❌';
                } else if (file.status === 'downloading') {
                    statusClass = 'status-downloading';
                    statusIcon = '⬇️';
                }

                fileItem.innerHTML = `
                    <span>${file.name}</span>
                    <span class="file-status ${statusClass}">${statusIcon} ${file.status}</span>
                `;

                fileList.appendChild(fileItem);
            });
        }

        function pauseDownload() {
            const isWindows = navigator.platform.indexOf('Win') > -1;
            const handlerUrl = isWindows ? 'download_handler_windows.php' : 'download_handler.php';

            fetch(handlerUrl, {
                method: 'POST',
                body: new URLSearchParams({action: 'pause'})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('pauseBtn').textContent = '▶️ Reanudar';
                    document.getElementById('pauseBtn').onclick = resumeDownload;
                    document.getElementById('progressText').textContent = 'Descarga pausada';
                }
            });
        }

        function resumeDownload() {
            const isWindows = navigator.platform.indexOf('Win') > -1;
            const handlerUrl = isWindows ? 'download_handler_windows.php' : 'download_handler.php';

            fetch(handlerUrl, {
                method: 'POST',
                body: new URLSearchParams({action: 'resume'})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('pauseBtn').textContent = '⏸️ Pausar';
                    document.getElementById('pauseBtn').onclick = pauseDownload;
                    document.getElementById('progressText').textContent = 'Descarga reanudada';
                }
            });
        }

        function cancelDownload() {
            if (confirm('¿Estás seguro de que quieres cancelar la descarga?')) {
                const isWindows = navigator.platform.indexOf('Win') > -1;
                const handlerUrl = isWindows ? 'download_handler_windows.php' : 'download_handler.php';

                fetch(handlerUrl, {
                    method: 'POST',
                    body: new URLSearchParams({action: 'cancel'})
                })
                .then(response => response.json())
                .then(data => {
                    hideDownloadControls();
                    document.getElementById('progressText').textContent = 'Descarga cancelada';
                    if (isAutoRefreshing) {
                        toggleAutoRefresh();
                    }
                });
            }
        }

        function toggleAutoRefresh() {
            if (isAutoRefreshing) {
                clearInterval(autoRefreshInterval);
                isAutoRefreshing = false;
                document.getElementById('autoRefreshBtn').textContent = '▶️ Auto-actualizar';
                document.getElementById('autoRefreshBtn').style.background = '';
            } else {
                autoRefreshInterval = setInterval(loadLog, 3000); // Cada 3 segundos
                isAutoRefreshing = true;
                document.getElementById('autoRefreshBtn').textContent = '⏸️ Detener Auto';
                document.getElementById('autoRefreshBtn').style.background = '#28a745';
                loadLog(); // Cargar inmediatamente
            }
        }

        function closeModal() {
            document.getElementById('downloadModal').style.display = 'none';
            if (downloadInterval) {
                clearInterval(downloadInterval);
            }
        }

        function loadLog() {
            fetch('get_log.php')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('logContent').textContent = data;
                    // Auto-scroll al final
                    const logElement = document.getElementById('logContent');
                    logElement.scrollTop = logElement.scrollHeight;
                })
                .catch(error => {
                    document.getElementById('logContent').textContent = 'Error cargando el log: ' + error;
                });
        }

        // Cerrar modal al hacer clic fuera de él
        window.onclick = function(event) {
            const modal = document.getElementById('downloadModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Auto-actualizar log cada 5 segundos si hay una descarga en progreso
        setInterval(function() {
            if (document.getElementById('logContent').textContent.includes('Descarga iniciada')) {
                loadLog();
            }
        }, 5000);

        // Funciones del explorador de directorios
        function openDirectoryBrowser() {
            document.getElementById('directoryBrowser').style.display = 'block';
            // Cargar directorios inmediatamente
            loadCurrentDirectory();
        }

        function closeDirectoryBrowser() {
            document.getElementById('directoryBrowser').style.display = 'none';
        }

        function loadCurrentDirectory() {
            const currentPath = document.getElementById('currentPath').textContent || '/';
            browseDirectory(currentPath);
        }

        function refreshDirectory() {
            loadCurrentDirectory();
        }

        function browseDirectory(path) {
            console.log('Navegando a:', path);

            // Mostrar indicador de carga
            document.getElementById('directoryList').innerHTML = '<div class="directory-item"><span class="directory-icon">⏳</span><span class="directory-name">Cargando directorio: ' + path + '</span></div>';

            // Hacer petición AJAX para cargar directorio usando el explorador simplificado
            const formData = new FormData();
            formData.append('action', 'list_directories');
            formData.append('path', path);

            // Usar el explorador optimizado para Hostinger
            fetch('hostinger_directory_browser.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Respuesta recibida:', response.status);
                if (!response.ok) {
                    throw new Error('Error HTTP: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Datos recibidos:', data);
                if (data.success) {
                    updateDirectoryListing(data.directories, data.currentPath);
                    document.getElementById('currentPath').textContent = data.currentPath;

                    if (data.message) {
                        showMessage(data.message, 'info');
                    }
                } else {
                    document.getElementById('directoryList').innerHTML = '<div class="directory-item"><span class="directory-icon">❌</span><span class="directory-name">Error: ' + (data.message || 'Error desconocido') + '</span></div>';

                    // Si falla, intentar mostrar rutas accesibles
                    showAccessiblePaths();
                }
            })
            .catch(error => {
                console.error('Error en la petición:', error);
                document.getElementById('directoryList').innerHTML = '<div class="directory-item"><span class="directory-icon">❌</span><span class="directory-name">Error de conexión: ' + error.message + '</span></div>';

                // Si falla, intentar mostrar rutas accesibles
                showAccessiblePaths();
            });
        }

        function showAccessiblePaths() {
            const formData = new FormData();
            formData.append('action', 'test_paths');

            fetch('hostinger_directory_browser.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.accessiblePaths.length > 0) {
                    let html = '<div class="directory-item"><span class="directory-icon">💡</span><span class="directory-name">Rutas accesibles encontradas:</span></div>';

                    data.accessiblePaths.forEach(path => {
                        html += `<div class="directory-item" onclick="browseDirectory('${path}')" style="cursor: pointer;">
                            <span class="directory-icon">📁</span>
                            <span class="directory-name">${path}</span>
                        </div>`;
                    });

                    document.getElementById('directoryList').innerHTML = html;
                }
            })
            .catch(error => {
                console.error('Error obteniendo rutas accesibles:', error);
            });
        }

        function updateDirectoryListing(directories, currentPath) {
            const listElement = document.getElementById('directoryList');
            listElement.innerHTML = '';

            if (!directories || directories.length === 0) {
                listElement.innerHTML = '<div class="directory-item"><span class="directory-icon">📂</span><span class="directory-name">Directorio vacío</span></div>';
                return;
            }

            // Botón para ir al directorio padre
            if (currentPath !== '/') {
                const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
                const parentItem = document.createElement('div');
                parentItem.className = 'directory-item';
                parentItem.style.cursor = 'pointer';
                parentItem.onclick = () => browseDirectory(parentPath);
                parentItem.innerHTML = '<span class="directory-icon">⬆️</span><span class="directory-name">.. (directorio padre)</span>';
                listElement.appendChild(parentItem);
            }

            // Separar directorios y archivos
            const dirs = directories.filter(item => item.type === 'directory');
            const files = directories.filter(item => item.type === 'file');

            // Mostrar directorios primero
            dirs.forEach(item => {
                const itemElement = document.createElement('div');
                itemElement.className = 'directory-item';
                itemElement.style.cursor = 'pointer';
                itemElement.onclick = () => browseDirectory(item.path);
                itemElement.innerHTML = `<span class="directory-icon">📁</span><span class="directory-name">${item.name}</span>`;
                listElement.appendChild(itemElement);
            });

            // Mostrar archivos después (solo para referencia)
            files.slice(0, 10).forEach(item => { // Limitar a 10 archivos para no saturar
                const itemElement = document.createElement('div');
                itemElement.className = 'directory-item';
                itemElement.style.opacity = '0.6';
                itemElement.style.cursor = 'default';
                itemElement.innerHTML = `<span class="directory-icon">📄</span><span class="directory-name">${item.name}</span>`;
                listElement.appendChild(itemElement);
            });

            if (files.length > 10) {
                const moreItem = document.createElement('div');
                moreItem.className = 'directory-item';
                moreItem.style.opacity = '0.6';
                moreItem.innerHTML = `<span class="directory-icon">📄</span><span class="directory-name">... y ${files.length - 10} archivos más</span>`;
                listElement.appendChild(moreItem);
            }
        }

        function showMessage(message, type) {
            // Crear elemento de mensaje temporal
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                padding: 10px 15px; border-radius: 5px; color: white;
                background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#17a2b8'};
            `;
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

        function selectCurrentPath() {
            const currentPath = document.getElementById('currentPath').textContent;
            document.getElementById('selected_path').value = currentPath;
            document.getElementById('download_path_hidden').value = currentPath;
            closeDirectoryBrowser();

            // Mostrar mensaje de confirmación
            const pathDisplay = document.createElement('div');
            pathDisplay.style.cssText = 'background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin-top: 10px;';
            pathDisplay.innerHTML = '✅ Ruta del servidor remoto seleccionada: ' + currentPath;

            const pathInput = document.getElementById('selected_path');
            const existingMessage = pathInput.parentNode.querySelector('.path-selected-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            pathDisplay.className = 'path-selected-message';
            pathInput.parentNode.appendChild(pathDisplay);

            // Remover el mensaje después de 3 segundos
            setTimeout(() => {
                if (pathDisplay.parentNode) {
                    pathDisplay.remove();
                }
            }, 3000);
        }

        function goToHome() {
            // Ir al directorio home del usuario en el servidor remoto
            browseDirectory('/home/<USER>'sftp_config']) ? $_SESSION['sftp_config']['username'] : 'usuario'; ?>');
        }

        // Manejar desconexión y reset
        <?php if (isset($_GET['disconnect'])): ?>
        <?php
        unset($_SESSION['sftp_config']);
        unset($_SESSION['sftp_test']);
        unset($_SESSION['series']);
        unset($_SESSION['m3u_file']);
        unset($_SESSION['download_config']);
        unset($_SESSION['current_browse_path']);
        ?>
        window.location.href = 'index.php';
        <?php endif; ?>

        <?php if (isset($_GET['reset_test'])): ?>
        <?php
        unset($_SESSION['sftp_test']);
        ?>
        window.location.href = 'index.php';
        <?php endif; ?>
    </script>
</body>
</html>
