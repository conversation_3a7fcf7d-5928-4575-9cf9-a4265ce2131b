<?php
/**
 * Alternativa para SFTP usando comandos del sistema
 * Para cuando la extensión SSH2 no está disponible
 */

class SFTPAlternative {
    private $host;
    private $port;
    private $username;
    private $password;
    
    public function __construct($host, $port, $username, $password) {
        $this->host = $host;
        $this->port = $port;
        $this->username = $username;
        $this->password = $password;
    }
    
    /**
     * Probar conexión SFTP usando comandos del sistema
     */
    public function testConnection() {
        try {
            // En Windows, intentar usar plink si está disponible
            if (PHP_OS_FAMILY === 'Windows') {
                return $this->testConnectionWindows();
            } else {
                return $this->testConnectionUnix();
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    private function testConnectionWindows() {
        // Verificar si plink está disponible
        $output = [];
        $returnCode = 0;
        exec('plink -V 2>&1', $output, $returnCode);
        
        if ($returnCode !== 0) {
            return [
                'success' => false, 
                'message' => 'PuTTY plink no está instalado. Instala PuTTY para usar SFTP en Windows.'
            ];
        }
        
        // Crear archivo temporal con comando
        $tempScript = tempnam(sys_get_temp_dir(), 'sftp_test');
        file_put_contents($tempScript, "echo 'pwd' | plink -ssh -P {$this->port} -l {$this->username} -pw {$this->password} {$this->host} 2>&1");
        
        $output = [];
        exec("cmd /c \"{$tempScript}\"", $output, $returnCode);
        unlink($tempScript);
        
        if ($returnCode === 0 && !empty($output)) {
            return ['success' => true, 'message' => 'Conexión exitosa'];
        } else {
            return ['success' => false, 'message' => 'Error de conexión: ' . implode(' ', $output)];
        }
    }
    
    private function testConnectionUnix() {
        // Verificar si sshpass está disponible
        $output = [];
        $returnCode = 0;
        exec('which sshpass 2>/dev/null', $output, $returnCode);
        
        if ($returnCode !== 0) {
            return [
                'success' => false, 
                'message' => 'sshpass no está instalado. Instala con: sudo apt-get install sshpass'
            ];
        }
        
        // Probar conexión
        $command = "sshpass -p " . escapeshellarg($this->password) . " ssh -p {$this->port} -o StrictHostKeyChecking=no -o ConnectTimeout=10 {$this->username}@{$this->host} 'pwd' 2>&1";
        
        $output = [];
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            return ['success' => true, 'message' => 'Conexión exitosa'];
        } else {
            return ['success' => false, 'message' => 'Error de conexión: ' . implode(' ', $output)];
        }
    }
    
    /**
     * Listar directorios remotos
     */
    public function listDirectory($path = '/') {
        try {
            if (PHP_OS_FAMILY === 'Windows') {
                return $this->listDirectoryWindows($path);
            } else {
                return $this->listDirectoryUnix($path);
            }
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function listDirectoryWindows($path) {
        // Crear script temporal para SFTP
        $tempScript = tempnam(sys_get_temp_dir(), 'sftp_list');
        $sftpCommands = "cd " . escapeshellarg($path) . "\nls -la\nquit\n";
        file_put_contents($tempScript, $sftpCommands);
        
        $command = "echo y | plink -sftp -P {$this->port} -l {$this->username} -pw {$this->password} {$this->host} -m \"{$tempScript}\" 2>&1";
        
        $output = [];
        exec($command, $output, $returnCode);
        unlink($tempScript);
        
        return $this->parseDirectoryListing($output);
    }
    
    private function listDirectoryUnix($path) {
        // Crear script temporal para SFTP
        $tempScript = tempnam(sys_get_temp_dir(), 'sftp_list');
        $sftpCommands = "cd " . escapeshellarg($path) . "\nls -la\nquit\n";
        file_put_contents($tempScript, $sftpCommands);
        
        $command = "sshpass -p " . escapeshellarg($this->password) . " sftp -P {$this->port} -o StrictHostKeyChecking=no -b \"{$tempScript}\" {$this->username}@{$this->host} 2>&1";
        
        $output = [];
        exec($command, $output, $returnCode);
        unlink($tempScript);
        
        return $this->parseDirectoryListing($output);
    }
    
    private function parseDirectoryListing($output) {
        $items = [];
        
        foreach ($output as $line) {
            $line = trim($line);
            
            // Buscar líneas que parecen listados de archivos (formato ls -la)
            if (preg_match('/^([d-])([rwx-]{9})\s+\d+\s+\S+\s+\S+\s+\d+\s+\S+\s+\d+\s+[\d:]+\s+(.+)$/', $line, $matches)) {
                $isDirectory = $matches[1] === 'd';
                $name = $matches[3];
                
                // Saltar . y ..
                if ($name === '.' || $name === '..') continue;
                
                $items[] = [
                    'name' => $name,
                    'type' => $isDirectory ? 'directory' : 'file',
                    'path' => rtrim($path, '/') . '/' . $name
                ];
            }
        }
        
        return $items;
    }
    
    /**
     * Crear directorio remoto
     */
    public function createDirectory($path) {
        if (PHP_OS_FAMILY === 'Windows') {
            return $this->createDirectoryWindows($path);
        } else {
            return $this->createDirectoryUnix($path);
        }
    }
    
    private function createDirectoryWindows($path) {
        $tempScript = tempnam(sys_get_temp_dir(), 'sftp_mkdir');
        $sftpCommands = "mkdir " . escapeshellarg($path) . "\nquit\n";
        file_put_contents($tempScript, $sftpCommands);
        
        $command = "echo y | plink -sftp -P {$this->port} -l {$this->username} -pw {$this->password} {$this->host} -m \"{$tempScript}\" 2>&1";
        
        $output = [];
        exec($command, $output, $returnCode);
        unlink($tempScript);
        
        return $returnCode === 0;
    }
    
    private function createDirectoryUnix($path) {
        $tempScript = tempnam(sys_get_temp_dir(), 'sftp_mkdir');
        $sftpCommands = "mkdir " . escapeshellarg($path) . "\nquit\n";
        file_put_contents($tempScript, $sftpCommands);
        
        $command = "sshpass -p " . escapeshellarg($this->password) . " sftp -P {$this->port} -o StrictHostKeyChecking=no -b \"{$tempScript}\" {$this->username}@{$this->host} 2>&1";
        
        $output = [];
        exec($command, $output, $returnCode);
        unlink($tempScript);
        
        return $returnCode === 0;
    }
    
    /**
     * Subir archivo
     */
    public function uploadFile($localFile, $remotePath) {
        if (PHP_OS_FAMILY === 'Windows') {
            return $this->uploadFileWindows($localFile, $remotePath);
        } else {
            return $this->uploadFileUnix($localFile, $remotePath);
        }
    }
    
    private function uploadFileWindows($localFile, $remotePath) {
        $tempScript = tempnam(sys_get_temp_dir(), 'sftp_upload');
        $sftpCommands = "put " . escapeshellarg($localFile) . " " . escapeshellarg($remotePath) . "\nquit\n";
        file_put_contents($tempScript, $sftpCommands);
        
        $command = "echo y | plink -sftp -P {$this->port} -l {$this->username} -pw {$this->password} {$this->host} -m \"{$tempScript}\" 2>&1";
        
        $output = [];
        exec($command, $output, $returnCode);
        unlink($tempScript);
        
        return $returnCode === 0;
    }
    
    private function uploadFileUnix($localFile, $remotePath) {
        $command = "sshpass -p " . escapeshellarg($this->password) . " scp -P {$this->port} -o StrictHostKeyChecking=no " . escapeshellarg($localFile) . " {$this->username}@{$this->host}:" . escapeshellarg($remotePath) . " 2>&1";
        
        $output = [];
        exec($command, $output, $returnCode);
        
        return $returnCode === 0;
    }
}

// Función de utilidad para usar en lugar de las funciones SSH2
function createSFTPConnection($host, $port, $username, $password) {
    return new SFTPAlternative($host, $port, $username, $password);
}
?>
