<?php
/**
 * Descargador de Series desde archivos M3U con SFTP
 * 
 * Este script lee un archivo M3U, identifica series por temporadas/episodios,
 * se conecta a un servidor SFTP y descarga los archivos organizándolos
 * en carpetas por serie y episodio.
 */

class M3USeriesDownloader {
    private $config;
    private $sftpConnection;
    private $sftpSession;
    
    public function __construct($configFile = 'config.php') {
        $this->config = require $configFile;
        $this->validateConfig();
    }
    
    /**
     * Valida la configuración
     */
    private function validateConfig() {
        $required = ['sftp.host', 'sftp.username', 'download.base_directory'];
        foreach ($required as $key) {
            if (!$this->getConfigValue($key)) {
                throw new Exception("Configuración requerida faltante: {$key}");
            }
        }
    }
    
    /**
     * Obtiene un valor de configuración usando notación de punto
     */
    private function getConfigValue($key) {
        $keys = explode('.', $key);
        $value = $this->config;
        foreach ($keys as $k) {
            if (!isset($value[$k])) return null;
            $value = $value[$k];
        }
        return $value;
    }
    
    /**
     * Conecta al servidor SFTP
     */
    public function connectSFTP() {
        $this->log("Conectando al servidor SFTP...");
        
        $this->sftpConnection = ssh2_connect(
            $this->getConfigValue('sftp.host'),
            $this->getConfigValue('sftp.port') ?: 22
        );
        
        if (!$this->sftpConnection) {
            throw new Exception("No se pudo conectar al servidor SFTP");
        }
        
        // Autenticación
        if ($this->getConfigValue('sftp.private_key')) {
            $auth = ssh2_auth_pubkey_file(
                $this->sftpConnection,
                $this->getConfigValue('sftp.username'),
                $this->getConfigValue('sftp.public_key'),
                $this->getConfigValue('sftp.private_key'),
                $this->getConfigValue('sftp.passphrase')
            );
        } else {
            $auth = ssh2_auth_password(
                $this->sftpConnection,
                $this->getConfigValue('sftp.username'),
                $this->getConfigValue('sftp.password')
            );
        }
        
        if (!$auth) {
            throw new Exception("Falló la autenticación SFTP");
        }
        
        $this->sftpSession = ssh2_sftp($this->sftpConnection);
        if (!$this->sftpSession) {
            throw new Exception("No se pudo inicializar la sesión SFTP");
        }
        
        $this->log("Conectado exitosamente al servidor SFTP");
    }
    
    /**
     * Lee y parsea un archivo M3U
     */
    public function parseM3U($filePath) {
        if (!file_exists($filePath)) {
            throw new Exception("Archivo M3U no encontrado: {$filePath}");
        }
        
        $this->log("Parseando archivo M3U: {$filePath}");
        
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);
        
        $entries = [];
        $currentEntry = null;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (empty($line)) continue;
            
            if (strpos($line, '#EXTINF:') === 0) {
                // Línea de información
                $currentEntry = [
                    'info' => $line,
                    'title' => $this->extractTitle($line),
                    'url' => null
                ];
            } elseif (!empty($line) && strpos($line, '#') !== 0) {
                // Línea de URL
                if ($currentEntry) {
                    $currentEntry['url'] = $line;
                    $entries[] = $currentEntry;
                    $currentEntry = null;
                }
            }
        }
        
        $this->log("Encontradas " . count($entries) . " entradas en el M3U");
        return $entries;
    }
    
    /**
     * Extrae el título de una línea EXTINF
     */
    private function extractTitle($extinf) {
        // Formato típico: #EXTINF:duration,title
        if (preg_match('/^#EXTINF:[^,]*,(.*)$/', $extinf, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }
    
    /**
     * Identifica series y episodios en las entradas del M3U
     */
    public function identifySeries($entries) {
        $series = [];
        
        foreach ($entries as $entry) {
            $title = $entry['title'];
            $seriesInfo = $this->extractSeriesInfo($title);
            
            if ($seriesInfo) {
                $seriesName = $seriesInfo['series'];
                $season = $seriesInfo['season'];
                $episode = $seriesInfo['episode'];
                
                if (!isset($series[$seriesName])) {
                    $series[$seriesName] = [];
                }
                
                if (!isset($series[$seriesName][$season])) {
                    $series[$seriesName][$season] = [];
                }
                
                $series[$seriesName][$season][$episode] = $entry;
            }
        }
        
        $this->log("Identificadas " . count($series) . " series diferentes");
        return $series;
    }
    
    /**
     * Extrae información de serie, temporada y episodio del título
     */
    private function extractSeriesInfo($title) {
        $patterns = $this->getConfigValue('patterns');
        
        // Buscar patrón S01E01
        if (preg_match($patterns['season_episode'], $title, $matches)) {
            $season = intval($matches[1]);
            $episode = intval($matches[2]);
            
            // Extraer nombre de la serie
            if (preg_match($patterns['series_name'], $title, $seriesMatches)) {
                $seriesName = trim($seriesMatches[1]);
            } else {
                // Fallback: tomar todo antes del patrón de temporada/episodio
                $seriesName = preg_replace($patterns['season_episode'], '', $title);
                $seriesName = trim($seriesName);
            }
            
            return [
                'series' => $this->cleanSeriesName($seriesName),
                'season' => $season,
                'episode' => $episode
            ];
        }
        
        // Buscar patrón alternativo 1x01
        if (preg_match($patterns['season_episode_alt'], $title, $matches)) {
            $season = intval($matches[1]);
            $episode = intval($matches[2]);
            
            $seriesName = preg_replace($patterns['season_episode_alt'], '', $title);
            $seriesName = trim($seriesName);
            
            return [
                'series' => $this->cleanSeriesName($seriesName),
                'season' => $season,
                'episode' => $episode
            ];
        }
        
        return null;
    }
    
    /**
     * Limpia el nombre de la serie para usar como nombre de carpeta
     */
    private function cleanSeriesName($name) {
        // Remover caracteres no válidos para nombres de archivo
        $name = preg_replace('/[<>:"/\\|?*]/', '', $name);
        $name = trim($name, '. ');
        return $name;
    }
    
    /**
     * Descarga una serie completa
     */
    public function downloadSeries($series, $selectedSeries = null) {
        $baseDir = $this->getConfigValue('download.base_directory');

        if (!is_dir($baseDir)) {
            mkdir($baseDir, 0755, true);
        }

        foreach ($series as $seriesName => $seasons) {
            if ($selectedSeries && $selectedSeries !== $seriesName) {
                continue;
            }

            $this->log("Descargando serie: {$seriesName}");
            $seriesDir = $baseDir . DIRECTORY_SEPARATOR . $seriesName;

            if (!is_dir($seriesDir)) {
                mkdir($seriesDir, 0755, true);
            }

            foreach ($seasons as $seasonNum => $episodes) {
                $seasonDir = $seriesDir . DIRECTORY_SEPARATOR . "Temporada {$seasonNum}";

                if (!is_dir($seasonDir)) {
                    mkdir($seasonDir, 0755, true);
                }

                foreach ($episodes as $episodeNum => $entry) {
                    $this->downloadEpisode($entry, $seasonDir, $seriesName, $seasonNum, $episodeNum);
                }
            }
        }
    }

    /**
     * Descarga un episodio individual
     */
    private function downloadEpisode($entry, $seasonDir, $seriesName, $seasonNum, $episodeNum) {
        $url = $entry['url'];
        $title = $entry['title'];

        // Generar nombre de archivo
        $extension = $this->getFileExtension($url);
        $filename = sprintf("S%02dE%02d - %s.%s",
            $seasonNum,
            $episodeNum,
            $this->cleanFileName($title),
            $extension
        );

        $filepath = $seasonDir . DIRECTORY_SEPARATOR . $filename;

        // Verificar si ya existe
        if (file_exists($filepath)) {
            $this->log("Archivo ya existe, saltando: {$filename}");
            return;
        }

        $this->log("Descargando: {$filename}");

        try {
            if (strpos($url, 'sftp://') === 0 || $this->sftpSession) {
                $this->downloadFromSFTP($url, $filepath);
            } else {
                $this->downloadFromHTTP($url, $filepath);
            }

            $this->log("Descarga completada: {$filename}");
        } catch (Exception $e) {
            $this->log("Error descargando {$filename}: " . $e->getMessage(), 'ERROR');
        }
    }

    /**
     * Descarga archivo desde SFTP
     */
    private function downloadFromSFTP($url, $localPath) {
        // Extraer ruta del servidor desde la URL
        $remotePath = parse_url($url, PHP_URL_PATH);

        $remoteStream = fopen("ssh2.sftp://{$this->sftpSession}{$remotePath}", 'r');
        if (!$remoteStream) {
            throw new Exception("No se pudo abrir archivo remoto: {$remotePath}");
        }

        $localStream = fopen($localPath, 'w');
        if (!$localStream) {
            fclose($remoteStream);
            throw new Exception("No se pudo crear archivo local: {$localPath}");
        }

        $chunkSize = $this->getConfigValue('download.chunk_size');
        while (!feof($remoteStream)) {
            $chunk = fread($remoteStream, $chunkSize);
            fwrite($localStream, $chunk);
        }

        fclose($remoteStream);
        fclose($localStream);
    }

    /**
     * Descarga archivo desde HTTP/HTTPS
     */
    private function downloadFromHTTP($url, $localPath) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => false,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => $this->getConfigValue('download.timeout'),
            CURLOPT_FILE => fopen($localPath, 'w'),
            CURLOPT_PROGRESSFUNCTION => [$this, 'downloadProgress'],
            CURLOPT_NOPROGRESS => false,
        ]);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($result === false || $httpCode >= 400) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception("Error en descarga HTTP: {$error} (Código: {$httpCode})");
        }

        curl_close($ch);
    }

    /**
     * Callback para mostrar progreso de descarga
     */
    public function downloadProgress($resource, $downloadSize, $downloaded, $uploadSize, $uploaded) {
        if ($downloadSize > 0) {
            $percent = round(($downloaded / $downloadSize) * 100, 2);
            echo "\rProgreso: {$percent}% ({$downloaded}/{$downloadSize} bytes)";
        }
    }

    /**
     * Obtiene la extensión de archivo desde una URL
     */
    private function getFileExtension($url) {
        $path = parse_url($url, PHP_URL_PATH);
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        return $extension ?: 'mp4'; // Default a mp4 si no se puede determinar
    }

    /**
     * Limpia un nombre de archivo
     */
    private function cleanFileName($name) {
        $name = preg_replace('/[<>:"/\\|?*]/', '', $name);
        $name = trim($name, '. ');
        return substr($name, 0, 100); // Limitar longitud
    }

    /**
     * Lista series disponibles
     */
    public function listSeries($series) {
        echo "\n=== SERIES DISPONIBLES ===\n";
        $index = 1;
        foreach ($series as $seriesName => $seasons) {
            $totalEpisodes = 0;
            foreach ($seasons as $episodes) {
                $totalEpisodes += count($episodes);
            }
            echo "{$index}. {$seriesName} (" . count($seasons) . " temporadas, {$totalEpisodes} episodios)\n";
            $index++;
        }
        echo "\n";
    }

    /**
     * Desconecta del servidor SFTP
     */
    public function disconnect() {
        if ($this->sftpConnection) {
            ssh2_disconnect($this->sftpConnection);
            $this->log("Desconectado del servidor SFTP");
        }
    }

    /**
     * Logging
     */
    private function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;

        echo $logMessage;

        if ($this->getConfigValue('logging.enabled')) {
            file_put_contents(
                $this->getConfigValue('logging.file'),
                $logMessage,
                FILE_APPEND | LOCK_EX
            );
        }
    }
}
