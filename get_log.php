<?php
/**
 * API para obtener el contenido del log en tiempo real
 */

header('Content-Type: text/plain');
header('Cache-Control: no-cache, must-revalidate');

// Cargar configuración para obtener la ruta del log
$config = require 'config.php';
$logFile = $config['logging']['file'] ?? './download.log';

if (file_exists($logFile)) {
    // Leer las últimas 100 líneas del log
    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    if ($lines) {
        // Mostrar las últimas 100 líneas
        $lastLines = array_slice($lines, -100);
        echo implode("\n", $lastLines);
    } else {
        echo "El archivo de log está vacío.";
    }
} else {
    echo "No hay log disponible aún. Inicia una descarga para ver el progreso aquí.";
}
?>
